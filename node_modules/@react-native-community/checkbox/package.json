{"name": "@react-native-community/checkbox", "version": "0.5.4", "description": "React Native Checkbox native modules for Android , iOS and Windows", "keywords": ["checkbox", "react native", "react-native"], "homepage": "https://github.com/react-native-community/react-native-checkbox#readme", "repository": {"type": "git", "url": "https://github.com/react-native-community/react-native-checkbox.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "main": "dist/CheckBox", "types": "typings/index.d.ts", "scripts": {"build": "tsc"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/plugin-proposal-class-properties": "^7.4.0", "@react-native-community/eslint-config": "^1.1.0", "@react-native-community/eslint-plugin": "^1.0.0", "@types/jest": "^24.0.25", "@types/react-native": "^0.62", "@types/react-test-renderer": "16.9.0", "babel-plugin-module-resolver": "^3.1.3", "detox": "^16.4.1", "eslint": "^6.8.0", "eslint-plugin-prettier": "^3.1.2", "jest": "^24.7.1", "metro-react-native-babel-preset": "0.59.0", "prettier": "^2.0.5", "react": "16.11.0", "react-native": "0.62", "react-native-windows": "^0.62", "react-test-renderer": "16.9.0", "ts-jest": "^24.2.0", "typescript": "^3.8.3", "xcpretty": "^0.2.8001"}, "peerDependencies": {"react": "*", "react-native": ">= 0.62", "react-native-windows": ">=0.62"}, "publishConfig": {"access": "public"}, "eslintConfig": {"extends": "@react-native-community", "env": {"es6": true, "jest": true}, "globals": {"it": true, "expect": true, "element": true, "describe": true, "by": true, "device": true, "beforeAll": true, "beforeEach": true, "afterAll": true, "jest": true, "jasmine": true}}}