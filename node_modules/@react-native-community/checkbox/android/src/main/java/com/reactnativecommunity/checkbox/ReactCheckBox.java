/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
package com.reactnativecommunity.checkbox;

import android.content.Context;
import androidx.appcompat.widget.AppCompatCheckBox;

/** CheckBox that has its value controlled by JS. */
/*package*/ class ReactCheckBox extends AppCompatCheckBox {

  private boolean mAllowChange;

  public ReactCheckBox(Context context) {
    super(context);
    mAllowChange = true;
  }

  @Override
  public void setChecked(boolean checked) {
    if (mAllowChange) {
      mAllowChange = false;
      super.setChecked(checked);
    }
  }

  /*package*/ void setOn(boolean on) {
    // If the checkbox has a different value than the value sent by J<PERSON>, we must change it.
    if (isChecked() != on) {
      super.setChecked(on);
    }
    mAllowChange = true;
  }
}
