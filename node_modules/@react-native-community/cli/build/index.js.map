{"version": 3, "names": ["pkgJson", "require", "program", "<PERSON><PERSON><PERSON><PERSON>", "usage", "version", "enablePositionalOptions", "handleError", "err", "logger", "enable", "opts", "verbose", "error", "message", "replace", "stack", "log", "hasDebugMessages", "info", "chalk", "dim", "reset", "process", "exit", "printExamples", "examples", "output", "length", "formattedUsage", "map", "example", "desc", "cyan", "cmd", "join", "concat", "bold", "isDetachedCommand", "command", "detached", "isAttachedCommand", "attachCommand", "config", "commands", "find", "name", "Error", "option", "action", "handleAction", "args", "passedOptions", "argv", "Array", "from", "slice", "func", "description", "addHelpText", "opt", "options", "parse", "val", "default", "run", "platformName", "setupAndRun", "e", "isCommandPassed", "commandName", "filter", "arg", "disable", "setVerbose", "includes", "platform", "scriptName", "absolutePath", "path", "__dirname", "childProcess", "execFileSync", "stdio", "warn", "red", "selectedPlatform", "platformIndex", "indexOf", "loadConfigAsync", "projectCommands", "Object", "values", "debug", "CLIError", "detachedCommands", "push", "bin", "resolve"], "sources": ["../src/index.ts"], "sourcesContent": ["import loadConfig, {loadConfigAsync} from '@react-native-community/cli-config';\nimport {CLIError, logger} from '@react-native-community/cli-tools';\nimport type {\n  Command,\n  Config,\n  DetachedCommand,\n} from '@react-native-community/cli-types';\nimport chalk from 'chalk';\nimport childProcess from 'child_process';\nimport {Command as CommanderCommand} from 'commander';\nimport path from 'path';\nimport {detachedCommands, projectCommands} from './commands';\n\nconst pkgJson = require('../package.json');\n\nconst program = new CommanderCommand()\n  .usage('[command] [options]')\n  .version(pkgJson.version, '-v --version', 'Output the current version')\n  .enablePositionalOptions();\n\nconst handleError = (err: Error) => {\n  logger.enable();\n  if (program.opts().verbose) {\n    logger.error(err.message);\n  } else {\n    // Some error messages (esp. custom ones) might have `.` at the end already.\n    const message = err.message.replace(/\\.$/, '');\n    logger.error(`${message}.`);\n  }\n  if (err.stack) {\n    logger.log(err.stack);\n  }\n  if (!program.opts().verbose && logger.hasDebugMessages()) {\n    logger.info(\n      chalk.dim(\n        `Run CLI with ${chalk.reset('--verbose')} ${chalk.dim(\n          'flag for more details.',\n        )}`,\n      ),\n    );\n  }\n  process.exit(1);\n};\n\nfunction printExamples(examples: Command['examples']) {\n  let output: string[] = [];\n\n  if (examples && examples.length > 0) {\n    const formattedUsage = examples\n      .map((example) => `  ${example.desc}: \\n  ${chalk.cyan(example.cmd)}`)\n      .join('\\n\\n');\n\n    output = output.concat([chalk.bold('\\nExample usage:'), formattedUsage]);\n  }\n\n  return output.join('\\n').concat('\\n');\n}\n\n/**\n * Custom type assertion needed for the `makeCommand` conditional\n * types to be properly resolved.\n */\nfunction isDetachedCommand(\n  command: Command<boolean>,\n): command is DetachedCommand {\n  return command.detached === true;\n}\n\nfunction isAttachedCommand(\n  command: Command<boolean>,\n): command is Command<false> {\n  return !isDetachedCommand(command);\n}\n\n/**\n * Attaches a new command onto global `commander` instance.\n *\n * Note that this function takes additional argument of `Config` type in case\n * passed `command` needs it for its execution.\n */\nfunction attachCommand<C extends Command<boolean>>(\n  command: C,\n  config: C extends DetachedCommand ? Config | undefined : Config,\n): void {\n  // commander@9.x will internally push commands into an array structure!\n  // Commands with duplicate names (e.g. from config) must be reduced before\n  // calling this function.\n  // https://unpkg.com/browse/commander@9.4.1/lib/command.js#L1308\n  if (program.commands.find((cmd) => cmd.name() === command.name)) {\n    throw new Error(\n      'Invariant Violation: Attempted to override an already registered ' +\n        `command: '${command.name}'. This is not supported by the underlying ` +\n        'library and will cause bugs. Ensure a command with this `name` is ' +\n        'only registered once.',\n    );\n  }\n\n  const cmd = program\n    .command(command.name)\n    .option('--verbose', 'Increase logging verbosity')\n    .action(async function handleAction(\n      this: CommanderCommand,\n      ...args: string[]\n    ) {\n      const passedOptions = this.opts();\n      const argv = Array.from(args).slice(0, -1);\n\n      try {\n        if (isDetachedCommand(command)) {\n          await command.func(argv, passedOptions, config);\n        } else if (isAttachedCommand(command)) {\n          await command.func(argv, config, passedOptions);\n        } else {\n          throw new Error('A command must be either attached or detached');\n        }\n      } catch (error) {\n        handleError(error as Error);\n      }\n    });\n\n  if (command.description) {\n    cmd.description(command.description);\n  }\n\n  cmd.addHelpText('after', printExamples(command.examples));\n\n  for (const opt of command.options || []) {\n    cmd.option(\n      opt.name,\n      opt.description ?? '',\n      opt.parse || ((val: any) => val),\n      typeof opt.default === 'function' ? opt.default(config) : opt.default,\n    );\n  }\n}\n\n// Platform name is optional argument passed by out of tree platforms.\nasync function run(platformName?: string) {\n  try {\n    await setupAndRun(platformName);\n  } catch (e) {\n    handleError(e as Error);\n  }\n}\n\nconst isCommandPassed = (commandName: string) => {\n  return process.argv.filter((arg) => arg === commandName).length > 0;\n};\n\nasync function setupAndRun(platformName?: string) {\n  // Commander is not available yet\n\n  // when we run `config`, we don't want to output anything to the console. We\n  // expect it to return valid JSON\n  if (isCommandPassed('config')) {\n    logger.disable();\n  }\n\n  logger.setVerbose(process.argv.includes('--verbose'));\n\n  // We only have a setup script for UNIX envs currently\n  if (process.platform !== 'win32') {\n    const scriptName = 'setup_env.sh';\n    const absolutePath = path.join(__dirname, '..', scriptName);\n\n    try {\n      childProcess.execFileSync(absolutePath, {stdio: 'pipe'});\n    } catch (error) {\n      logger.warn(\n        `Failed to run environment setup script \"${scriptName}\"\\n\\n${chalk.red(\n          error,\n        )}`,\n      );\n      logger.info(\n        `React Native CLI will continue to run if your local environment matches what React Native expects. If it does fail, check out \"${absolutePath}\" and adjust your environment to match it.`,\n      );\n    }\n  }\n\n  let config: Config | undefined;\n  try {\n    let selectedPlatform: string | undefined;\n\n    /*\n      When linking dependencies in iOS and Android build we're passing `--platform` argument,\n    to only load the configuration for the specific platform.\n    */\n    if (isCommandPassed('config')) {\n      const platformIndex = process.argv.indexOf('--platform');\n\n      if (platformIndex !== -1 && platformIndex < process.argv.length - 1) {\n        selectedPlatform = process.argv[platformIndex + 1];\n      }\n    }\n\n    config = await loadConfigAsync({\n      selectedPlatform,\n    });\n\n    logger.enable();\n\n    const commands: Record<string, Command> = {};\n\n    // Reduce overridden commands before registering\n    for (const command of [...projectCommands, ...config.commands]) {\n      commands[command.name] = command;\n    }\n\n    for (const command of Object.values(commands)) {\n      attachCommand(command, config);\n    }\n  } catch (error) {\n    /**\n     * When there is no `package.json` found, the CLI will enter `detached` mode and a subset\n     * of commands will be available. That's why we don't throw on such kind of error.\n     */\n    if ((error as Error).message.includes(\"We couldn't find a package.json\")) {\n      logger.debug((error as Error).message);\n      logger.debug(\n        'Failed to load configuration of your project. Only a subset of commands will be available.',\n      );\n    } else {\n      throw new CLIError(\n        'Failed to load configuration of your project.',\n        error as any,\n      );\n    }\n  } finally {\n    for (const command of detachedCommands) {\n      attachCommand(command, config);\n    }\n  }\n\n  const argv = [...process.argv];\n\n  // If out of tree platform specifices custom platform name, we need to pass it to argv array for the init command.\n  if (isCommandPassed('init') && platformName) {\n    argv.push('--platform-name', platformName);\n  }\n\n  program.parse(argv);\n}\n\nconst bin = require.resolve('./bin');\n\nexport {run, bin, loadConfig, loadConfigAsync};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA6D;AAAA;AAAA;AAE7D,MAAMA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAE1C,MAAMC,OAAO,GAAG,KAAIC,oBAAgB,GAAE,CACnCC,KAAK,CAAC,qBAAqB,CAAC,CAC5BC,OAAO,CAACL,OAAO,CAACK,OAAO,EAAE,cAAc,EAAE,4BAA4B,CAAC,CACtEC,uBAAuB,EAAE;AAE5B,MAAMC,WAAW,GAAIC,GAAU,IAAK;EAClCC,kBAAM,CAACC,MAAM,EAAE;EACf,IAAIR,OAAO,CAACS,IAAI,EAAE,CAACC,OAAO,EAAE;IAC1BH,kBAAM,CAACI,KAAK,CAACL,GAAG,CAACM,OAAO,CAAC;EAC3B,CAAC,MAAM;IACL;IACA,MAAMA,OAAO,GAAGN,GAAG,CAACM,OAAO,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC9CN,kBAAM,CAACI,KAAK,CAAE,GAAEC,OAAQ,GAAE,CAAC;EAC7B;EACA,IAAIN,GAAG,CAACQ,KAAK,EAAE;IACbP,kBAAM,CAACQ,GAAG,CAACT,GAAG,CAACQ,KAAK,CAAC;EACvB;EACA,IAAI,CAACd,OAAO,CAACS,IAAI,EAAE,CAACC,OAAO,IAAIH,kBAAM,CAACS,gBAAgB,EAAE,EAAE;IACxDT,kBAAM,CAACU,IAAI,CACTC,gBAAK,CAACC,GAAG,CACN,gBAAeD,gBAAK,CAACE,KAAK,CAAC,WAAW,CAAE,IAAGF,gBAAK,CAACC,GAAG,CACnD,wBAAwB,CACxB,EAAC,CACJ,CACF;EACH;EACAE,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;AACjB,CAAC;AAED,SAASC,aAAa,CAACC,QAA6B,EAAE;EACpD,IAAIC,MAAgB,GAAG,EAAE;EAEzB,IAAID,QAAQ,IAAIA,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;IACnC,MAAMC,cAAc,GAAGH,QAAQ,CAC5BI,GAAG,CAAEC,OAAO,IAAM,KAAIA,OAAO,CAACC,IAAK,SAAQZ,gBAAK,CAACa,IAAI,CAACF,OAAO,CAACG,GAAG,CAAE,EAAC,CAAC,CACrEC,IAAI,CAAC,MAAM,CAAC;IAEfR,MAAM,GAAGA,MAAM,CAACS,MAAM,CAAC,CAAChB,gBAAK,CAACiB,IAAI,CAAC,kBAAkB,CAAC,EAAER,cAAc,CAAC,CAAC;EAC1E;EAEA,OAAOF,MAAM,CAACQ,IAAI,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,IAAI,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA,SAASE,iBAAiB,CACxBC,OAAyB,EACG;EAC5B,OAAOA,OAAO,CAACC,QAAQ,KAAK,IAAI;AAClC;AAEA,SAASC,iBAAiB,CACxBF,OAAyB,EACE;EAC3B,OAAO,CAACD,iBAAiB,CAACC,OAAO,CAAC;AACpC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,aAAa,CACpBH,OAAU,EACVI,MAA+D,EACzD;EACN;EACA;EACA;EACA;EACA,IAAIzC,OAAO,CAAC0C,QAAQ,CAACC,IAAI,CAAEX,GAAG,IAAKA,GAAG,CAACY,IAAI,EAAE,KAAKP,OAAO,CAACO,IAAI,CAAC,EAAE;IAC/D,MAAM,IAAIC,KAAK,CACb,mEAAmE,GAChE,aAAYR,OAAO,CAACO,IAAK,6CAA4C,GACtE,oEAAoE,GACpE,uBAAuB,CAC1B;EACH;EAEA,MAAMZ,GAAG,GAAGhC,OAAO,CAChBqC,OAAO,CAACA,OAAO,CAACO,IAAI,CAAC,CACrBE,MAAM,CAAC,WAAW,EAAE,4BAA4B,CAAC,CACjDC,MAAM,CAAC,eAAeC,YAAY,CAEjC,GAAGC,IAAc,EACjB;IACA,MAAMC,aAAa,GAAG,IAAI,CAACzC,IAAI,EAAE;IACjC,MAAM0C,IAAI,GAAGC,KAAK,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAE1C,IAAI;MACF,IAAIlB,iBAAiB,CAACC,OAAO,CAAC,EAAE;QAC9B,MAAMA,OAAO,CAACkB,IAAI,CAACJ,IAAI,EAAED,aAAa,EAAET,MAAM,CAAC;MACjD,CAAC,MAAM,IAAIF,iBAAiB,CAACF,OAAO,CAAC,EAAE;QACrC,MAAMA,OAAO,CAACkB,IAAI,CAACJ,IAAI,EAAEV,MAAM,EAAES,aAAa,CAAC;MACjD,CAAC,MAAM;QACL,MAAM,IAAIL,KAAK,CAAC,+CAA+C,CAAC;MAClE;IACF,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdN,WAAW,CAACM,KAAK,CAAU;IAC7B;EACF,CAAC,CAAC;EAEJ,IAAI0B,OAAO,CAACmB,WAAW,EAAE;IACvBxB,GAAG,CAACwB,WAAW,CAACnB,OAAO,CAACmB,WAAW,CAAC;EACtC;EAEAxB,GAAG,CAACyB,WAAW,CAAC,OAAO,EAAElC,aAAa,CAACc,OAAO,CAACb,QAAQ,CAAC,CAAC;EAEzD,KAAK,MAAMkC,GAAG,IAAIrB,OAAO,CAACsB,OAAO,IAAI,EAAE,EAAE;IACvC3B,GAAG,CAACc,MAAM,CACRY,GAAG,CAACd,IAAI,EACRc,GAAG,CAACF,WAAW,IAAI,EAAE,EACrBE,GAAG,CAACE,KAAK,KAAMC,GAAQ,IAAKA,GAAG,CAAC,EAChC,OAAOH,GAAG,CAACI,OAAO,KAAK,UAAU,GAAGJ,GAAG,CAACI,OAAO,CAACrB,MAAM,CAAC,GAAGiB,GAAG,CAACI,OAAO,CACtE;EACH;AACF;;AAEA;AACA,eAAeC,GAAG,CAACC,YAAqB,EAAE;EACxC,IAAI;IACF,MAAMC,WAAW,CAACD,YAAY,CAAC;EACjC,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV7D,WAAW,CAAC6D,CAAC,CAAU;EACzB;AACF;AAEA,MAAMC,eAAe,GAAIC,WAAmB,IAAK;EAC/C,OAAO/C,OAAO,CAAC8B,IAAI,CAACkB,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAKF,WAAW,CAAC,CAAC1C,MAAM,GAAG,CAAC;AACrE,CAAC;AAED,eAAeuC,WAAW,CAACD,YAAqB,EAAE;EAChD;;EAEA;EACA;EACA,IAAIG,eAAe,CAAC,QAAQ,CAAC,EAAE;IAC7B5D,kBAAM,CAACgE,OAAO,EAAE;EAClB;EAEAhE,kBAAM,CAACiE,UAAU,CAACnD,OAAO,CAAC8B,IAAI,CAACsB,QAAQ,CAAC,WAAW,CAAC,CAAC;;EAErD;EACA,IAAIpD,OAAO,CAACqD,QAAQ,KAAK,OAAO,EAAE;IAChC,MAAMC,UAAU,GAAG,cAAc;IACjC,MAAMC,YAAY,GAAGC,eAAI,CAAC5C,IAAI,CAAC6C,SAAS,EAAE,IAAI,EAAEH,UAAU,CAAC;IAE3D,IAAI;MACFI,wBAAY,CAACC,YAAY,CAACJ,YAAY,EAAE;QAACK,KAAK,EAAE;MAAM,CAAC,CAAC;IAC1D,CAAC,CAAC,OAAOtE,KAAK,EAAE;MACdJ,kBAAM,CAAC2E,IAAI,CACR,2CAA0CP,UAAW,QAAOzD,gBAAK,CAACiE,GAAG,CACpExE,KAAK,CACL,EAAC,CACJ;MACDJ,kBAAM,CAACU,IAAI,CACR,kIAAiI2D,YAAa,4CAA2C,CAC3L;IACH;EACF;EAEA,IAAInC,MAA0B;EAC9B,IAAI;IACF,IAAI2C,gBAAoC;;IAExC;AACJ;AACA;AACA;IACI,IAAIjB,eAAe,CAAC,QAAQ,CAAC,EAAE;MAC7B,MAAMkB,aAAa,GAAGhE,OAAO,CAAC8B,IAAI,CAACmC,OAAO,CAAC,YAAY,CAAC;MAExD,IAAID,aAAa,KAAK,CAAC,CAAC,IAAIA,aAAa,GAAGhE,OAAO,CAAC8B,IAAI,CAACzB,MAAM,GAAG,CAAC,EAAE;QACnE0D,gBAAgB,GAAG/D,OAAO,CAAC8B,IAAI,CAACkC,aAAa,GAAG,CAAC,CAAC;MACpD;IACF;IAEA5C,MAAM,GAAG,MAAM,IAAA8C,4BAAe,EAAC;MAC7BH;IACF,CAAC,CAAC;IAEF7E,kBAAM,CAACC,MAAM,EAAE;IAEf,MAAMkC,QAAiC,GAAG,CAAC,CAAC;;IAE5C;IACA,KAAK,MAAML,OAAO,IAAI,CAAC,GAAGmD,yBAAe,EAAE,GAAG/C,MAAM,CAACC,QAAQ,CAAC,EAAE;MAC9DA,QAAQ,CAACL,OAAO,CAACO,IAAI,CAAC,GAAGP,OAAO;IAClC;IAEA,KAAK,MAAMA,OAAO,IAAIoD,MAAM,CAACC,MAAM,CAAChD,QAAQ,CAAC,EAAE;MAC7CF,aAAa,CAACH,OAAO,EAAEI,MAAM,CAAC;IAChC;EACF,CAAC,CAAC,OAAO9B,KAAK,EAAE;IACd;AACJ;AACA;AACA;IACI,IAAKA,KAAK,CAAWC,OAAO,CAAC6D,QAAQ,CAAC,iCAAiC,CAAC,EAAE;MACxElE,kBAAM,CAACoF,KAAK,CAAEhF,KAAK,CAAWC,OAAO,CAAC;MACtCL,kBAAM,CAACoF,KAAK,CACV,4FAA4F,CAC7F;IACH,CAAC,MAAM;MACL,MAAM,KAAIC,oBAAQ,EAChB,+CAA+C,EAC/CjF,KAAK,CACN;IACH;EACF,CAAC,SAAS;IACR,KAAK,MAAM0B,OAAO,IAAIwD,0BAAgB,EAAE;MACtCrD,aAAa,CAACH,OAAO,EAAEI,MAAM,CAAC;IAChC;EACF;EAEA,MAAMU,IAAI,GAAG,CAAC,GAAG9B,OAAO,CAAC8B,IAAI,CAAC;;EAE9B;EACA,IAAIgB,eAAe,CAAC,MAAM,CAAC,IAAIH,YAAY,EAAE;IAC3Cb,IAAI,CAAC2C,IAAI,CAAC,iBAAiB,EAAE9B,YAAY,CAAC;EAC5C;EAEAhE,OAAO,CAAC4D,KAAK,CAACT,IAAI,CAAC;AACrB;AAEA,MAAM4C,GAAG,GAAGhG,OAAO,CAACiG,OAAO,CAAC,OAAO,CAAC;AAAC"}