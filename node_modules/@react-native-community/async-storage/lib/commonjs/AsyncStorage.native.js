'use strict';var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _RCTAsyncStorage=_interopRequireDefault(require("./RCTAsyncStorage"));if(!_RCTAsyncStorage.default){throw new Error("[@RNC/AsyncStorage]: NativeModule: AsyncStorage is null.\n\nTo fix this issue try these steps:\n\n  \u2022 Run `react-native link @react-native-community/async-storage` in the project root.\n\n  \u2022 Rebuild and restart the app.\n\n  \u2022 Run the packager with `--reset-cache` flag.\n\n  \u2022 If you are using CocoaPods on iOS, run `pod install` in the `ios` directory and then rebuild and re-run the app.\n\n  \u2022 If this happens while testing with Jest, check out docs how to integrate AsyncStorage with it: https://react-native-community.github.io/async-storage/docs/advanced/jest\n\nIf none of these fix the issue, please open an issue on the Github repository: https://github.com/react-native-community/react-native-async-storage/issues \n");}function checkValidInput(usedKey,value){var isValuePassed=arguments.length>1;if(typeof usedKey!=='string'){console.warn("[AsyncStorage] Using ".concat(typeof usedKey," type for key is not supported. This can lead to unexpected behavior/errors. Use string instead.\nKey passed: ").concat(usedKey,"\n"));}if(isValuePassed&&typeof value!=='string'){if(value==null){throw new Error("[AsyncStorage] Passing null/undefined as value is not supported. If you want to remove value, Use .remove method instead.\nPassed value: ".concat(value,"\nPassed key: ").concat(usedKey,"\n"));}else{console.warn("[AsyncStorage] The value for key \"".concat(usedKey,"\" is not a string. This can lead to unexpected behavior/errors. Consider stringifying it.\nPassed value: ").concat(value,"\nPassed key: ").concat(usedKey,"\n"));}}}var AsyncStorage={_getRequests:[],_getKeys:[],_immediate:null,getItem:function getItem(key,callback){return new Promise(function(resolve,reject){checkValidInput(key);_RCTAsyncStorage.default.multiGet([key],function(errors,result){var value=result&&result[0]&&result[0][1]?result[0][1]:null;var errs=convertErrors(errors);callback&&callback(errs&&errs[0],value);if(errs){reject(errs[0]);}else{resolve(value);}});});},setItem:function setItem(key,value,callback){return new Promise(function(resolve,reject){checkValidInput(key,value);_RCTAsyncStorage.default.multiSet([[key,value]],function(errors){var errs=convertErrors(errors);callback&&callback(errs&&errs[0]);if(errs){reject(errs[0]);}else{resolve(null);}});});},removeItem:function removeItem(key,callback){return new Promise(function(resolve,reject){checkValidInput(key);_RCTAsyncStorage.default.multiRemove([key],function(errors){var errs=convertErrors(errors);callback&&callback(errs&&errs[0]);if(errs){reject(errs[0]);}else{resolve(null);}});});},mergeItem:function mergeItem(key,value,callback){return new Promise(function(resolve,reject){checkValidInput(key,value);_RCTAsyncStorage.default.multiMerge([[key,value]],function(errors){var errs=convertErrors(errors);callback&&callback(errs&&errs[0]);if(errs){reject(errs[0]);}else{resolve(null);}});});},clear:function clear(callback){return new Promise(function(resolve,reject){_RCTAsyncStorage.default.clear(function(error){var err=convertError(error);callback&&callback(err);if(err){reject(err);}else{resolve(null);}});});},getAllKeys:function getAllKeys(callback){return new Promise(function(resolve,reject){_RCTAsyncStorage.default.getAllKeys(function(error,keys){var err=convertError(error);callback&&callback(err,keys);if(err){reject(err);}else{resolve(keys);}});});},flushGetRequests:function flushGetRequests(){var getRequests=this._getRequests;var getKeys=this._getKeys;this._getRequests=[];this._getKeys=[];_RCTAsyncStorage.default.multiGet(getKeys,function(errors,result){var map={};result&&result.forEach(function(_ref){var _ref2=(0,_slicedToArray2.default)(_ref,2),key=_ref2[0],value=_ref2[1];map[key]=value;return value;});var reqLength=getRequests.length;for(var i=0;i<reqLength;i++){var request=getRequests[i];var requestKeys=request.keys;var requestResult=requestKeys.map(function(key){return[key,map[key]];});request.callback&&request.callback(null,requestResult);request.resolve&&request.resolve(requestResult);}});},multiGet:function multiGet(keys,callback){var _this=this;if(!this._immediate){this._immediate=setImmediate(function(){_this._immediate=null;_this.flushGetRequests();});}var getRequest={keys:keys,callback:callback,keyIndex:this._getKeys.length,resolve:null,reject:null};var promiseResult=new Promise(function(resolve,reject){getRequest.resolve=resolve;getRequest.reject=reject;});this._getRequests.push(getRequest);keys.forEach(function(key){if(_this._getKeys.indexOf(key)===-1){_this._getKeys.push(key);}});return promiseResult;},multiSet:function multiSet(keyValuePairs,callback){return new Promise(function(resolve,reject){keyValuePairs.forEach(function(_ref3){var _ref4=(0,_slicedToArray2.default)(_ref3,2),key=_ref4[0],value=_ref4[1];checkValidInput(key,value);});_RCTAsyncStorage.default.multiSet(keyValuePairs,function(errors){var error=convertErrors(errors);callback&&callback(error);if(error){reject(error);}else{resolve(null);}});});},multiRemove:function multiRemove(keys,callback){return new Promise(function(resolve,reject){keys.forEach(function(key){return checkValidInput(key);});_RCTAsyncStorage.default.multiRemove(keys,function(errors){var error=convertErrors(errors);callback&&callback(error);if(error){reject(error);}else{resolve(null);}});});},multiMerge:function multiMerge(keyValuePairs,callback){return new Promise(function(resolve,reject){_RCTAsyncStorage.default.multiMerge(keyValuePairs,function(errors){var error=convertErrors(errors);callback&&callback(error);if(error){reject(error);}else{resolve(null);}});});}};if(!_RCTAsyncStorage.default.multiMerge){delete AsyncStorage.mergeItem;delete AsyncStorage.multiMerge;}function convertErrors(errs){if(!errs||Array.isArray(errs)&&errs.length===0){return null;}return(Array.isArray(errs)?errs:[errs]).map(function(e){return convertError(e);});}function convertError(error){if(!error){return null;}var out=new Error(error.message);out.key=error.key;return out;}var _default=AsyncStorage;exports.default=_default;
//# sourceMappingURL=AsyncStorage.native.js.map