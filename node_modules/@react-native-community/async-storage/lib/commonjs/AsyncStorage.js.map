{"version": 3, "sources": ["AsyncStorage.js"], "names": ["mergeLocalStorageItem", "key", "value", "oldValue", "window", "localStorage", "getItem", "oldObject", "JSON", "parse", "newObject", "nextValue", "stringify", "setItem", "createPromise", "getValue", "callback", "Promise", "resolve", "reject", "err", "createPromiseAll", "promises", "processResult", "all", "then", "result", "errors", "AsyncStorage", "removeItem", "clear", "numberOfKeys", "length", "keys", "i", "push", "map", "keyValuePairs", "item", "mergeItem"], "mappings": "sWAUA,+DAEA,GAAMA,CAAAA,qBAAqB,CAAG,QAAxBA,CAAAA,qBAAwB,CAACC,GAAD,CAAMC,KAAN,CAAgB,CAC5C,GAAMC,CAAAA,QAAQ,CAAGC,MAAM,CAACC,YAAP,CAAoBC,OAApB,CAA4BL,GAA5B,CAAjB,CACA,GAAMM,CAAAA,SAAS,CAAGC,IAAI,CAACC,KAAL,CAAWN,QAAX,CAAlB,CACA,GAAMO,CAAAA,SAAS,CAAGF,IAAI,CAACC,KAAL,CAAWP,KAAX,CAAlB,CACA,GAAMS,CAAAA,SAAS,CAAGH,IAAI,CAACI,SAAL,CAAe,wBAAM,EAAN,CAAUL,SAAV,CAAqBG,SAArB,CAAf,CAAlB,CACAN,MAAM,CAACC,YAAP,CAAoBQ,OAApB,CAA4BZ,GAA5B,CAAiCU,SAAjC,EACD,CAND,CAQA,GAAMG,CAAAA,aAAa,CAAG,QAAhBA,CAAAA,aAAgB,CAACC,QAAD,CAAWC,QAAX,CAAoC,CACxD,MAAO,IAAIC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACtC,GAAI,CACF,GAAMjB,CAAAA,KAAK,CAAGa,QAAQ,EAAtB,CACA,GAAIC,QAAJ,CAAc,CACZA,QAAQ,CAAC,IAAD,CAAOd,KAAP,CAAR,CACD,CACDgB,OAAO,CAAChB,KAAD,CAAP,CACD,CAAC,MAAOkB,GAAP,CAAY,CACZ,GAAIJ,QAAJ,CAAc,CACZA,QAAQ,CAACI,GAAD,CAAR,CACD,CACDD,MAAM,CAACC,GAAD,CAAN,CACD,CACF,CAbM,CAAP,CAcD,CAfD,CAiBA,GAAMC,CAAAA,gBAAgB,CAAG,QAAnBA,CAAAA,gBAAmB,CAACC,QAAD,CAAWN,QAAX,CAAqBO,aAArB,CAAmD,CAC1E,MAAON,CAAAA,OAAO,CAACO,GAAR,CAAYF,QAAZ,EAAsBG,IAAtB,CACL,SAAAC,MAAM,CAAI,CACR,GAAMxB,CAAAA,KAAK,CAAGqB,aAAa,CAAGA,aAAa,CAACG,MAAD,CAAhB,CAA2B,IAAtD,CACAV,QAAQ,EAAIA,QAAQ,CAAC,IAAD,CAAOd,KAAP,CAApB,CACA,MAAOe,CAAAA,OAAO,CAACC,OAAR,CAAgBhB,KAAhB,CAAP,CACD,CALI,CAML,SAAAyB,MAAM,CAAI,CACRX,QAAQ,EAAIA,QAAQ,CAACW,MAAD,CAApB,CACA,MAAOV,CAAAA,OAAO,CAACE,MAAR,CAAeQ,MAAf,CAAP,CACD,CATI,CAAP,CAWD,CAZD,C,GAcqBC,CAAAA,Y,wKAKJ3B,G,CAAae,Q,CAAiC,CAC3D,MAAOF,CAAAA,aAAa,CAAC,UAAM,CACzB,MAAOV,CAAAA,MAAM,CAACC,YAAP,CAAoBC,OAApB,CAA4BL,GAA5B,CAAP,CACD,CAFmB,CAEjBe,QAFiB,CAApB,CAGD,C,wCAKcf,G,CAAaC,K,CAAec,Q,CAAiC,CAC1E,MAAOF,CAAAA,aAAa,CAAC,UAAM,CACzBV,MAAM,CAACC,YAAP,CAAoBQ,OAApB,CAA4BZ,GAA5B,CAAiCC,KAAjC,EACD,CAFmB,CAEjBc,QAFiB,CAApB,CAGD,C,8CAKiBf,G,CAAae,Q,CAAiC,CAC9D,MAAOF,CAAAA,aAAa,CAAC,UAAM,CACzB,MAAOV,CAAAA,MAAM,CAACC,YAAP,CAAoBwB,UAApB,CAA+B5B,GAA/B,CAAP,CACD,CAFmB,CAEjBe,QAFiB,CAApB,CAGD,C,4CAKgBf,G,CAAaC,K,CAAec,Q,CAAiC,CAC5E,MAAOF,CAAAA,aAAa,CAAC,UAAM,CACzBd,qBAAqB,CAACC,GAAD,CAAMC,KAAN,CAArB,CACD,CAFmB,CAEjBc,QAFiB,CAApB,CAGD,C,oCAKYA,Q,CAAiC,CAC5C,MAAOF,CAAAA,aAAa,CAAC,UAAM,CACzBV,MAAM,CAACC,YAAP,CAAoByB,KAApB,GACD,CAFmB,CAEjBd,QAFiB,CAApB,CAGD,C,8CAKiBA,Q,CAAiC,CACjD,MAAOF,CAAAA,aAAa,CAAC,UAAM,CACzB,GAAMiB,CAAAA,YAAY,CAAG3B,MAAM,CAACC,YAAP,CAAoB2B,MAAzC,CACA,GAAMC,CAAAA,IAAI,CAAG,EAAb,CACA,IAAK,GAAIC,CAAAA,CAAC,CAAG,CAAb,CAAgBA,CAAC,CAAGH,YAApB,CAAkCG,CAAC,EAAI,CAAvC,CAA0C,CACxC,GAAMjC,CAAAA,GAAG,CAAGG,MAAM,CAACC,YAAP,CAAoBJ,GAApB,CAAwBiC,CAAxB,CAAZ,CACAD,IAAI,CAACE,IAAL,CAAUlC,GAAV,EACD,CACD,MAAOgC,CAAAA,IAAP,CACD,CARmB,CAQjBjB,QARiB,CAApB,CASD,C,2DAKyB,CAAE,C,0CAQZiB,I,CAAqBjB,Q,CAAiC,CACpE,GAAMM,CAAAA,QAAQ,CAAGW,IAAI,CAACG,GAAL,CAAS,SAAAnC,GAAG,QAAI2B,CAAAA,YAAY,CAACtB,OAAb,CAAqBL,GAArB,CAAJ,EAAZ,CAAjB,CACA,GAAMsB,CAAAA,aAAa,CAAG,QAAhBA,CAAAA,aAAgB,CAAAG,MAAM,QAAIA,CAAAA,MAAM,CAACU,GAAP,CAAW,SAAClC,KAAD,CAAQgC,CAAR,QAAc,CAACD,IAAI,CAACC,CAAD,CAAL,CAAUhC,KAAV,CAAd,EAAX,CAAJ,EAA5B,CACA,MAAOmB,CAAAA,gBAAgB,CAACC,QAAD,CAAWN,QAAX,CAAqBO,aAArB,CAAvB,CACD,C,0CAMec,a,CAAqCrB,Q,CAAiC,CACpF,GAAMM,CAAAA,QAAQ,CAAGe,aAAa,CAACD,GAAd,CAAkB,SAAAE,IAAI,QAAIV,CAAAA,YAAY,CAACf,OAAb,CAAqByB,IAAI,CAAC,CAAD,CAAzB,CAA8BA,IAAI,CAAC,CAAD,CAAlC,CAAJ,EAAtB,CAAjB,CACA,MAAOjB,CAAAA,gBAAgB,CAACC,QAAD,CAAWN,QAAX,CAAvB,CACD,C,gDAKkBiB,I,CAAqBjB,Q,CAAiC,CACvE,GAAMM,CAAAA,QAAQ,CAAGW,IAAI,CAACG,GAAL,CAAS,SAAAnC,GAAG,QAAI2B,CAAAA,YAAY,CAACC,UAAb,CAAwB5B,GAAxB,CAAJ,EAAZ,CAAjB,CACA,MAAOoB,CAAAA,gBAAgB,CAACC,QAAD,CAAWN,QAAX,CAAvB,CACD,C,8CAQiBqB,a,CAAqCrB,Q,CAAiC,CACtF,GAAMM,CAAAA,QAAQ,CAAGe,aAAa,CAACD,GAAd,CAAkB,SAAAE,IAAI,QAAIV,CAAAA,YAAY,CAACW,SAAb,CAAuBD,IAAI,CAAC,CAAD,CAA3B,CAAgCA,IAAI,CAAC,CAAD,CAApC,CAAJ,EAAtB,CAAjB,CACA,MAAOjB,CAAAA,gBAAgB,CAACC,QAAD,CAAWN,QAAX,CAAvB,CACD,C", "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @flow\n */\n\nimport merge from 'deep-assign';\n\nconst mergeLocalStorageItem = (key, value) => {\n  const oldValue = window.localStorage.getItem(key);\n  const oldObject = JSON.parse(oldValue);\n  const newObject = JSON.parse(value);\n  const nextValue = JSON.stringify(merge({}, oldObject, newObject));\n  window.localStorage.setItem(key, nextValue);\n};\n\nconst createPromise = (getValue, callback): Promise<*> => {\n  return new Promise((resolve, reject) => {\n    try {\n      const value = getValue();\n      if (callback) {\n        callback(null, value);\n      }\n      resolve(value);\n    } catch (err) {\n      if (callback) {\n        callback(err);\n      }\n      reject(err);\n    }\n  });\n};\n\nconst createPromiseAll = (promises, callback, processResult): Promise<*> => {\n  return Promise.all(promises).then(\n    result => {\n      const value = processResult ? processResult(result) : null;\n      callback && callback(null, value);\n      return Promise.resolve(value);\n    },\n    errors => {\n      callback && callback(errors);\n      return Promise.reject(errors);\n    }\n  );\n};\n\nexport default class AsyncStorage {\n\n  /**\n   * Fetches `key` value.\n   */\n  static getItem(key: string, callback?: Function): Promise<*> {\n    return createPromise(() => {\n      return window.localStorage.getItem(key);\n    }, callback);\n  }\n\n  /**\n   * Sets `value` for `key`.\n   */\n  static setItem(key: string, value: string, callback?: Function): Promise<*> {\n    return createPromise(() => {\n      window.localStorage.setItem(key, value);\n    }, callback);\n  }\n\n  /**\n   * Removes a `key`\n   */\n  static removeItem(key: string, callback?: Function): Promise<*> {\n    return createPromise(() => {\n      return window.localStorage.removeItem(key);\n    }, callback);\n  }\n\n  /**\n   * Merges existing value with input value, assuming they are stringified JSON.\n   */\n  static mergeItem(key: string, value: string, callback?: Function): Promise<*> {\n    return createPromise(() => {\n      mergeLocalStorageItem(key, value);\n    }, callback);\n  }\n\n  /**\n   * Erases *all* AsyncStorage for the domain.\n   */\n  static clear(callback?: Function): Promise<*> {\n    return createPromise(() => {\n      window.localStorage.clear();\n    }, callback);\n  }\n\n  /**\n   * Gets *all* keys known to the app, for all callers, libraries, etc.\n   */\n  static getAllKeys(callback?: Function): Promise<*> {\n    return createPromise(() => {\n      const numberOfKeys = window.localStorage.length;\n      const keys = [];\n      for (let i = 0; i < numberOfKeys; i += 1) {\n        const key = window.localStorage.key(i);\n        keys.push(key);\n      }\n      return keys;\n    }, callback);\n  }\n\n  /**\n   * (stub) Flushes any pending requests using a single batch call to get the data.\n   */\n  static flushGetRequests() {}\n\n  /**\n   * multiGet resolves to an array of key-value pair arrays that matches the\n   * input format of multiSet.\n   *\n   *   multiGet(['k1', 'k2']) -> [['k1', 'val1'], ['k2', 'val2']]\n   */\n  static multiGet(keys: Array<string>, callback?: Function): Promise<*> {\n    const promises = keys.map(key => AsyncStorage.getItem(key));\n    const processResult = result => result.map((value, i) => [keys[i], value]);\n    return createPromiseAll(promises, callback, processResult);\n  }\n\n  /**\n   * Takes an array of key-value array pairs.\n   *   multiSet([['k1', 'val1'], ['k2', 'val2']])\n   */\n  static multiSet(keyValuePairs: Array<Array<string>>, callback?: Function): Promise<*> {\n    const promises = keyValuePairs.map(item => AsyncStorage.setItem(item[0], item[1]));\n    return createPromiseAll(promises, callback);\n  }\n\n  /**\n   * Delete all the keys in the `keys` array.\n   */\n  static multiRemove(keys: Array<string>, callback?: Function): Promise<*> {\n    const promises = keys.map(key => AsyncStorage.removeItem(key));\n    return createPromiseAll(promises, callback);\n  }\n\n  /**\n   * Takes an array of key-value array pairs and merges them with existing\n   * values, assuming they are stringified JSON.\n   *\n   *   multiMerge([['k1', 'val1'], ['k2', 'val2']])\n   */\n  static multiMerge(keyValuePairs: Array<Array<string>>, callback?: Function): Promise<*> {\n    const promises = keyValuePairs.map(item => AsyncStorage.mergeItem(item[0], item[1]));\n    return createPromiseAll(promises, callback);\n  }\n}\n"]}