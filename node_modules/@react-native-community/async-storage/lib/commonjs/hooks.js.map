{"version": 3, "sources": ["hooks.js"], "names": ["useAsyncStorage", "key", "getItem", "args", "AsyncStorage", "setItem", "mergeItem", "removeItem"], "mappings": "iMAIA,oEAiBO,QAASA,CAAAA,eAAT,CAAyBC,GAAzB,CAAwD,CAC7D,MAAO,CACLC,OAAO,CAAE,iDAAIC,IAAJ,0CAAIA,IAAJ,8BAAaC,uBAAaF,OAAb,8BAAqBD,GAArB,SAA6BE,IAA7B,EAAb,EADJ,CAELE,OAAO,CAAE,kDAAIF,IAAJ,+CAAIA,IAAJ,gCAAaC,uBAAaC,OAAb,8BAAqBJ,GAArB,SAA6BE,IAA7B,EAAb,EAFJ,CAGLG,SAAS,CAAE,oDAAIH,IAAJ,+CAAIA,IAAJ,gCAAaC,uBAAaE,SAAb,8BAAuBL,GAAvB,SAA+BE,IAA/B,EAAb,EAHN,CAILI,UAAU,CAAE,qDAAIJ,IAAJ,+CAAIA,IAAJ,gCAAaC,uBAAaG,UAAb,8BAAwBN,GAAxB,SAAgCE,IAAhC,EAAb,EAJP,CAAP,CAMD", "sourcesContent": ["/**\n * @format\n */\n\nimport AsyncStorage from './AsyncStorage';\n\ntype AsyncStorageHook = {\n  getItem: (\n    callback?: ?(error: ?Error, result: string | null) => void,\n  ) => Promise<string | null>,\n  setItem: (\n    value: string,\n    callback?: ?(error: ?Error) => void,\n  ) => Promise<null>,\n  mergeItem: (\n    value: string,\n    callback?: ?(error: ?Error) => void,\n  ) => Promise<null>,\n  removeItem: (callback?: ?(error: ?Error) => void) => Promise<null>,\n};\n\nexport function useAsyncStorage(key: string): AsyncStorageHook {\n  return {\n    getItem: (...args) => AsyncStorage.getItem(key, ...args),\n    setItem: (...args) => AsyncStorage.setItem(key, ...args),\n    mergeItem: (...args) => AsyncStorage.mergeItem(key, ...args),\n    removeItem: (...args) => AsyncStorage.removeItem(key, ...args),\n  };\n}\n"]}