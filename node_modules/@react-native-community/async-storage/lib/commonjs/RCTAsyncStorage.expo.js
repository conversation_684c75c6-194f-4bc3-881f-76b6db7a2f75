"use strict";Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;var _require=require('react-native'),NativeModules=_require.NativeModules,TurboModuleRegistry=_require.TurboModuleRegistry;var RCTAsyncStorage;if(TurboModuleRegistry){RCTAsyncStorage=TurboModuleRegistry.get("AsyncSQLiteDBStorage")||TurboModuleRegistry.get("AsyncLocalStorage");}else{RCTAsyncStorage=NativeModules.AsyncSQLiteDBStorage||NativeModules.AsyncLocalStorage;}var _default=RCTAsyncStorage;exports.default=_default;
//# sourceMappingURL=RCTAsyncStorage.expo.js.map