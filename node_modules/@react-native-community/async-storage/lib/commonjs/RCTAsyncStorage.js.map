{"version": 3, "sources": ["RCTAsyncStorage.js"], "names": ["require", "NativeModules", "RCTAsyncStorage", "PlatformLocalStorage", "RNC_AsyncSQLiteDBStorage", "RNCAsyncStorage"], "mappings": "0GAAwBA,OAAO,CAAC,cAAD,C,CAAxBC,a,UAAAA,a,CAEP,GAAMC,CAAAA,eAAe,CACnBD,aAAa,CAACE,oBAAd,EACAF,aAAa,CAACG,wBADd,EAEAH,aAAa,CAACI,eAHhB,C,aAKeH,e", "sourcesContent": ["const {NativeModules} = require('react-native');\n\nconst RCTAsyncStorage =\n  NativeModules.PlatformLocalStorage || // Support for external modules, like react-native-windows\n  NativeModules.RNC_AsyncSQLiteDBStorage ||\n  NativeModules.RNCAsyncStorage;\n\nexport default RCTAsyncStorage;"]}