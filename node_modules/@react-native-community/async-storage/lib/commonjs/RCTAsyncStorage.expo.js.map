{"version": 3, "sources": ["RCTAsyncStorage.expo.js"], "names": ["require", "NativeModules", "TurboModuleRegistry", "RCTAsyncStorage", "get", "AsyncSQLiteDBStorage", "AsyncLocalStorage"], "mappings": "0GAS6CA,OAAO,CAAC,cAAD,C,CAA7CC,a,UAAAA,a,CAAeC,mB,UAAAA,mB,CAEtB,GAAIC,CAAAA,eAAJ,CAMA,GAAID,mBAAJ,CAAyB,CACvBC,eAAe,CAAGD,mBAAmB,CAACE,GAApB,CAAwB,sBAAxB,GAAmDF,mBAAmB,CAACE,GAApB,CAAwB,mBAAxB,CAArE,CACD,CAFD,IAEO,CACLD,eAAe,CAAGF,aAAa,CAACI,oBAAd,EAAsCJ,aAAa,CAACK,iBAAtE,CACD,C,aAEcH,e", "sourcesContent": ["/**\n * Expo managed apps don't include the @react-native-community/async-storage\n * native modules yet, but the API interface is the same, so we can use the version\n * exported from React Native still.\n *\n * If in future releases (eg: @react-native-community/async-storage >= 2.0.0) this\n * will likely not be valid anymore, and the package will need to be included in the Expo SDK\n * to continue to work.\n */\nconst {NativeModules, TurboModuleRegistry} = require('react-native');\n\nlet RCTAsyncStorage;\n\n// TurboModuleRegistry falls back to NativeModules\n// so we don't have to try go assign NativeModules'\n// counterparts if TurboModuleRegistry would resolve\n// with undefined.\nif (TurboModuleRegistry) {\n  RCTAsyncStorage = TurboModuleRegistry.get(\"AsyncSQLiteDBStorage\") || TurboModuleRegistry.get(\"AsyncLocalStorage\");\n} else {\n  RCTAsyncStorage = NativeModules.AsyncSQLiteDBStorage || NativeModules.AsyncLocalStorage;\n}\n\nexport default RCTAsyncStorage;\n"]}