{"version": 3, "sources": ["AsyncStorage.native.js"], "names": ["RCTAsyncStorage", "Error", "checkValidInput", "usedKey", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arguments", "length", "console", "warn", "AsyncStorage", "_getRequests", "_get<PERSON>eys", "_immediate", "getItem", "key", "callback", "Promise", "resolve", "reject", "multiGet", "errors", "result", "errs", "convertErrors", "setItem", "multiSet", "removeItem", "multiRemove", "mergeItem", "multiMerge", "clear", "error", "err", "convertError", "getAllKeys", "keys", "flushGetRequests", "getRequests", "get<PERSON><PERSON><PERSON>", "map", "for<PERSON>ach", "reqL<PERSON>th", "i", "request", "requestKeys", "requestResult", "setImmediate", "getRequest", "keyIndex", "promiseResult", "push", "indexOf", "keyValuePairs", "Array", "isArray", "e", "out", "message"], "mappings": "AAWA,a,+PAEA,0EAEA,GAAI,CAACA,wBAAL,CAAsB,CACpB,KAAM,IAAIC,CAAAA,KAAJ,qvBAAN,CAgBD,CAiBD,QAASC,CAAAA,eAAT,CAAyBC,OAAzB,CAA0CC,KAA1C,CAAsD,CACpD,GAAMC,CAAAA,aAAa,CAAGC,SAAS,CAACC,MAAV,CAAmB,CAAzC,CAEA,GAAI,MAAOJ,CAAAA,OAAP,GAAmB,QAAvB,CAAiC,CAC/BK,OAAO,CAACC,IAAR,gCAC0B,MAAON,CAAAA,OADjC,0HACyJA,OADzJ,QAGD,CAED,GAAIE,aAAa,EAAI,MAAOD,CAAAA,KAAP,GAAiB,QAAtC,CAAgD,CAC9C,GAAIA,KAAK,EAAI,IAAb,CAAmB,CACjB,KAAM,IAAIH,CAAAA,KAAJ,oJACwIG,KADxI,0BAC8JD,OAD9J,OAAN,CAGD,CAJD,IAIO,CACLK,OAAO,CAACC,IAAR,8CACuCN,OADvC,sHAC0JC,KAD1J,0BACgLD,OADhL,QAGD,CACF,CACF,CASD,GAAMO,CAAAA,YAAY,CAAG,CACnBC,YAAY,CAAG,EADI,CAEnBC,QAAQ,CAAG,EAFQ,CAGnBC,UAAU,CAAG,IAHM,CAUnBC,OAAO,CAAE,iBACPC,GADO,CAEPC,QAFO,CAGiB,CACxB,MAAO,IAAIC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACtCjB,eAAe,CAACa,GAAD,CAAf,CACAf,yBAAgBoB,QAAhB,CAAyB,CAACL,GAAD,CAAzB,CAAgC,SAASM,MAAT,CAAiBC,MAAjB,CAAyB,CAEvD,GAAMlB,CAAAA,KAAK,CAAGkB,MAAM,EAAIA,MAAM,CAAC,CAAD,CAAhB,EAAuBA,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,CAAvB,CAAsCA,MAAM,CAAC,CAAD,CAAN,CAAU,CAAV,CAAtC,CAAqD,IAAnE,CACA,GAAMC,CAAAA,IAAI,CAAGC,aAAa,CAACH,MAAD,CAA1B,CACAL,QAAQ,EAAIA,QAAQ,CAACO,IAAI,EAAIA,IAAI,CAAC,CAAD,CAAb,CAAkBnB,KAAlB,CAApB,CACA,GAAImB,IAAJ,CAAU,CACRJ,MAAM,CAACI,IAAI,CAAC,CAAD,CAAL,CAAN,CACD,CAFD,IAEO,CACLL,OAAO,CAACd,KAAD,CAAP,CACD,CACF,CAVD,EAWD,CAbM,CAAP,CAcD,CA5BkB,CAmCnBqB,OAAO,CAAE,iBACPV,GADO,CAEPX,KAFO,CAGPY,QAHO,CAIQ,CACf,MAAO,IAAIC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACtCjB,eAAe,CAACa,GAAD,CAAMX,KAAN,CAAf,CACAJ,yBAAgB0B,QAAhB,CAAyB,CAAC,CAACX,GAAD,CAAMX,KAAN,CAAD,CAAzB,CAAyC,SAASiB,MAAT,CAAiB,CACxD,GAAME,CAAAA,IAAI,CAAGC,aAAa,CAACH,MAAD,CAA1B,CACAL,QAAQ,EAAIA,QAAQ,CAACO,IAAI,EAAIA,IAAI,CAAC,CAAD,CAAb,CAApB,CACA,GAAIA,IAAJ,CAAU,CACRJ,MAAM,CAACI,IAAI,CAAC,CAAD,CAAL,CAAN,CACD,CAFD,IAEO,CACLL,OAAO,CAAC,IAAD,CAAP,CACD,CACF,CARD,EASD,CAXM,CAAP,CAYD,CApDkB,CA2DnBS,UAAU,CAAE,oBACVZ,GADU,CAEVC,QAFU,CAGK,CACf,MAAO,IAAIC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACtCjB,eAAe,CAACa,GAAD,CAAf,CACAf,yBAAgB4B,WAAhB,CAA4B,CAACb,GAAD,CAA5B,CAAmC,SAASM,MAAT,CAAiB,CAClD,GAAME,CAAAA,IAAI,CAAGC,aAAa,CAACH,MAAD,CAA1B,CACAL,QAAQ,EAAIA,QAAQ,CAACO,IAAI,EAAIA,IAAI,CAAC,CAAD,CAAb,CAApB,CACA,GAAIA,IAAJ,CAAU,CACRJ,MAAM,CAACI,IAAI,CAAC,CAAD,CAAL,CAAN,CACD,CAFD,IAEO,CACLL,OAAO,CAAC,IAAD,CAAP,CACD,CACF,CARD,EASD,CAXM,CAAP,CAYD,CA3EkB,CAqFnBW,SAAS,CAAE,mBACTd,GADS,CAETX,KAFS,CAGTY,QAHS,CAIM,CACf,MAAO,IAAIC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACtCjB,eAAe,CAACa,GAAD,CAAMX,KAAN,CAAf,CACAJ,yBAAgB8B,UAAhB,CAA2B,CAAC,CAACf,GAAD,CAAMX,KAAN,CAAD,CAA3B,CAA2C,SAASiB,MAAT,CAAiB,CAC1D,GAAME,CAAAA,IAAI,CAAGC,aAAa,CAACH,MAAD,CAA1B,CACAL,QAAQ,EAAIA,QAAQ,CAACO,IAAI,EAAIA,IAAI,CAAC,CAAD,CAAb,CAApB,CACA,GAAIA,IAAJ,CAAU,CACRJ,MAAM,CAACI,IAAI,CAAC,CAAD,CAAL,CAAN,CACD,CAFD,IAEO,CACLL,OAAO,CAAC,IAAD,CAAP,CACD,CACF,CARD,EASD,CAXM,CAAP,CAYD,CAtGkB,CA+GnBa,KAAK,CAAE,eAASf,QAAT,CAA6D,CAClE,MAAO,IAAIC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACtCnB,yBAAgB+B,KAAhB,CAAsB,SAASC,KAAT,CAAgB,CACpC,GAAMC,CAAAA,GAAG,CAAGC,YAAY,CAACF,KAAD,CAAxB,CACAhB,QAAQ,EAAIA,QAAQ,CAACiB,GAAD,CAApB,CACA,GAAIA,GAAJ,CAAS,CACPd,MAAM,CAACc,GAAD,CAAN,CACD,CAFD,IAEO,CACLf,OAAO,CAAC,IAAD,CAAP,CACD,CACF,CARD,EASD,CAVM,CAAP,CAWD,CA3HkB,CAkInBiB,UAAU,CAAE,oBACVnB,QADU,CAEoB,CAC9B,MAAO,IAAIC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACtCnB,yBAAgBmC,UAAhB,CAA2B,SAASH,KAAT,CAAgBI,IAAhB,CAAsB,CAC/C,GAAMH,CAAAA,GAAG,CAAGC,YAAY,CAACF,KAAD,CAAxB,CACAhB,QAAQ,EAAIA,QAAQ,CAACiB,GAAD,CAAMG,IAAN,CAApB,CACA,GAAIH,GAAJ,CAAS,CACPd,MAAM,CAACc,GAAD,CAAN,CACD,CAFD,IAEO,CACLf,OAAO,CAACkB,IAAD,CAAP,CACD,CACF,CARD,EASD,CAVM,CAAP,CAWD,CAhJkB,CAiKnBC,gBAAgB,CAAE,2BAAiB,CACjC,GAAMC,CAAAA,WAAW,CAAG,KAAK3B,YAAzB,CACA,GAAM4B,CAAAA,OAAO,CAAG,KAAK3B,QAArB,CAEA,KAAKD,YAAL,CAAoB,EAApB,CACA,KAAKC,QAAL,CAAgB,EAAhB,CAEAZ,yBAAgBoB,QAAhB,CAAyBmB,OAAzB,CAAkC,SAASlB,MAAT,CAAiBC,MAAjB,CAAyB,CAOzD,GAAMkB,CAAAA,GAAG,CAAG,EAAZ,CACAlB,MAAM,EACJA,MAAM,CAACmB,OAAP,CAAe,cAAkB,+CAAhB1B,GAAgB,UAAXX,KAAW,UAC/BoC,GAAG,CAACzB,GAAD,CAAH,CAAWX,KAAX,CACA,MAAOA,CAAAA,KAAP,CACD,CAHD,CADF,CAKA,GAAMsC,CAAAA,SAAS,CAAGJ,WAAW,CAAC/B,MAA9B,CACA,IAAK,GAAIoC,CAAAA,CAAC,CAAG,CAAb,CAAgBA,CAAC,CAAGD,SAApB,CAA+BC,CAAC,EAAhC,CAAoC,CAClC,GAAMC,CAAAA,OAAO,CAAGN,WAAW,CAACK,CAAD,CAA3B,CACA,GAAME,CAAAA,WAAW,CAAGD,OAAO,CAACR,IAA5B,CACA,GAAMU,CAAAA,aAAa,CAAGD,WAAW,CAACL,GAAZ,CAAgB,SAAAzB,GAAG,QAAI,CAACA,GAAD,CAAMyB,GAAG,CAACzB,GAAD,CAAT,CAAJ,EAAnB,CAAtB,CACA6B,OAAO,CAAC5B,QAAR,EAAoB4B,OAAO,CAAC5B,QAAR,CAAiB,IAAjB,CAAuB8B,aAAvB,CAApB,CACAF,OAAO,CAAC1B,OAAR,EAAmB0B,OAAO,CAAC1B,OAAR,CAAgB4B,aAAhB,CAAnB,CACD,CACF,CArBD,EAsBD,CA9LkB,CAuMnB1B,QAAQ,CAAE,kBACRgB,IADQ,CAERpB,QAFQ,CAGuC,gBAC/C,GAAI,CAAC,KAAKH,UAAV,CAAsB,CACpB,KAAKA,UAAL,CAAkBkC,YAAY,CAAC,UAAM,CACnC,KAAI,CAAClC,UAAL,CAAkB,IAAlB,CACA,KAAI,CAACwB,gBAAL,GACD,CAH6B,CAA9B,CAID,CAED,GAAMW,CAAAA,UAAwB,CAAG,CAC/BZ,IAAI,CAAEA,IADyB,CAE/BpB,QAAQ,CAAEA,QAFqB,CAI/BiC,QAAQ,CAAE,KAAKrC,QAAL,CAAcL,MAJO,CAK/BW,OAAO,CAAE,IALsB,CAM/BC,MAAM,CAAE,IANuB,CAAjC,CASA,GAAM+B,CAAAA,aAAa,CAAG,GAAIjC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACrD6B,UAAU,CAAC9B,OAAX,CAAqBA,OAArB,CACA8B,UAAU,CAAC7B,MAAX,CAAoBA,MAApB,CACD,CAHqB,CAAtB,CAKA,KAAKR,YAAL,CAAkBwC,IAAlB,CAAuBH,UAAvB,EAEAZ,IAAI,CAACK,OAAL,CAAa,SAAA1B,GAAG,CAAI,CAClB,GAAI,KAAI,CAACH,QAAL,CAAcwC,OAAd,CAAsBrC,GAAtB,IAA+B,CAAC,CAApC,CAAuC,CACrC,KAAI,CAACH,QAAL,CAAcuC,IAAd,CAAmBpC,GAAnB,EACD,CACF,CAJD,EAMA,MAAOmC,CAAAA,aAAP,CACD,CAzOkB,CAiPnBxB,QAAQ,CAAE,kBACR2B,aADQ,CAERrC,QAFQ,CAGO,CACf,MAAO,IAAIC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACtCkC,aAAa,CAACZ,OAAd,CAAsB,eAAkB,gDAAhB1B,GAAgB,UAAXX,KAAW,UACtCF,eAAe,CAACa,GAAD,CAAMX,KAAN,CAAf,CACD,CAFD,EAIAJ,yBAAgB0B,QAAhB,CAAyB2B,aAAzB,CAAwC,SAAShC,MAAT,CAAiB,CACvD,GAAMW,CAAAA,KAAK,CAAGR,aAAa,CAACH,MAAD,CAA3B,CACAL,QAAQ,EAAIA,QAAQ,CAACgB,KAAD,CAApB,CACA,GAAIA,KAAJ,CAAW,CACTb,MAAM,CAACa,KAAD,CAAN,CACD,CAFD,IAEO,CACLd,OAAO,CAAC,IAAD,CAAP,CACD,CACF,CARD,EASD,CAdM,CAAP,CAeD,CApQkB,CA2QnBU,WAAW,CAAE,qBACXQ,IADW,CAEXpB,QAFW,CAGI,CACf,MAAO,IAAIC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACtCiB,IAAI,CAACK,OAAL,CAAa,SAAA1B,GAAG,QAAIb,CAAAA,eAAe,CAACa,GAAD,CAAnB,EAAhB,EAEAf,yBAAgB4B,WAAhB,CAA4BQ,IAA5B,CAAkC,SAASf,MAAT,CAAiB,CACjD,GAAMW,CAAAA,KAAK,CAAGR,aAAa,CAACH,MAAD,CAA3B,CACAL,QAAQ,EAAIA,QAAQ,CAACgB,KAAD,CAApB,CACA,GAAIA,KAAJ,CAAW,CACTb,MAAM,CAACa,KAAD,CAAN,CACD,CAFD,IAEO,CACLd,OAAO,CAAC,IAAD,CAAP,CACD,CACF,CARD,EASD,CAZM,CAAP,CAaD,CA5RkB,CAsSnBY,UAAU,CAAE,oBACVuB,aADU,CAEVrC,QAFU,CAGK,CACf,MAAO,IAAIC,CAAAA,OAAJ,CAAY,SAACC,OAAD,CAAUC,MAAV,CAAqB,CACtCnB,yBAAgB8B,UAAhB,CAA2BuB,aAA3B,CAA0C,SAAShC,MAAT,CAAiB,CACzD,GAAMW,CAAAA,KAAK,CAAGR,aAAa,CAACH,MAAD,CAA3B,CACAL,QAAQ,EAAIA,QAAQ,CAACgB,KAAD,CAApB,CACA,GAAIA,KAAJ,CAAW,CACTb,MAAM,CAACa,KAAD,CAAN,CACD,CAFD,IAEO,CACLd,OAAO,CAAC,IAAD,CAAP,CACD,CACF,CARD,EASD,CAVM,CAAP,CAWD,CArTkB,CAArB,CAyTA,GAAI,CAAClB,yBAAgB8B,UAArB,CAAiC,CAC/B,MAAOpB,CAAAA,YAAY,CAACmB,SAApB,CACA,MAAOnB,CAAAA,YAAY,CAACoB,UAApB,CACD,CAED,QAASN,CAAAA,aAAT,CAAuBD,IAAvB,CAAsD,CACpD,GAAI,CAACA,IAAD,EAAU+B,KAAK,CAACC,OAAN,CAAchC,IAAd,GAAuBA,IAAI,CAAChB,MAAL,GAAgB,CAArD,CAAyD,CACvD,MAAO,KAAP,CACD,CACD,MAAO,CAAC+C,KAAK,CAACC,OAAN,CAAchC,IAAd,EAAsBA,IAAtB,CAA6B,CAACA,IAAD,CAA9B,EAAsCiB,GAAtC,CAA0C,SAAAgB,CAAC,QAAItB,CAAAA,YAAY,CAACsB,CAAD,CAAhB,EAA3C,CAAP,CACD,CAED,QAAStB,CAAAA,YAAT,CAAsBF,KAAtB,CAAqC,CACnC,GAAI,CAACA,KAAL,CAAY,CACV,MAAO,KAAP,CACD,CACD,GAAMyB,CAAAA,GAAG,CAAG,GAAIxD,CAAAA,KAAJ,CAAU+B,KAAK,CAAC0B,OAAhB,CAAZ,CAEAD,GAAG,CAAC1C,GAAJ,CAAUiB,KAAK,CAACjB,GAAhB,CACA,MAAO0C,CAAAA,GAAP,CACD,C,aAEc/C,Y", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * @flow\n * @jsdoc\n */\n\n'use strict';\n\nimport RCTAsyncStorage from './RCTAsyncStorage';\n\nif (!RCTAsyncStorage) {\n  throw new Error(`[@RNC/AsyncStorage]: NativeModule: AsyncStorage is null.\n\nTo fix this issue try these steps:\n\n  • Run \\`react-native link @react-native-community/async-storage\\` in the project root.\n\n  • Rebuild and restart the app.\n\n  • Run the packager with \\`--reset-cache\\` flag.\n\n  • If you are using CocoaPods on iOS, run \\`pod install\\` in the \\`ios\\` directory and then rebuild and re-run the app.\n\n  • If this happens while testing with Je<PERSON>, check out docs how to integrate AsyncStorage with it: https://react-native-community.github.io/async-storage/docs/advanced/jest\n\nIf none of these fix the issue, please open an issue on the Github repository: https://github.com/react-native-community/react-native-async-storage/issues \n`);\n}\n\ntype ReadOnlyArrayString = $ReadOnlyArray<string>;\n\ntype MultiGetCallbackFunction = (\n  errors: ?$ReadOnlyArray<Error>,\n  result: ?$ReadOnlyArray<ReadOnlyArrayString>,\n) => void;\n\ntype MultiRequest = {|\n  keys: $ReadOnlyArray<string>,\n  callback: ?MultiGetCallbackFunction,\n  keyIndex: number,\n  resolve: ?(result?: Promise<?$ReadOnlyArray<ReadOnlyArrayString>>) => void,\n  reject: ?(error?: any) => void,\n|};\n\nfunction checkValidInput(usedKey: string, value: any) {\n  const isValuePassed = arguments.length > 1;\n\n  if (typeof usedKey !== 'string') {\n    console.warn(\n      `[AsyncStorage] Using ${typeof usedKey} type for key is not supported. This can lead to unexpected behavior/errors. Use string instead.\\nKey passed: ${usedKey}\\n`,\n    );\n  }\n\n  if (isValuePassed && typeof value !== 'string') {\n    if (value == null) {\n      throw new Error(\n        `[AsyncStorage] Passing null/undefined as value is not supported. If you want to remove value, Use .remove method instead.\\nPassed value: ${value}\\nPassed key: ${usedKey}\\n`,\n      );\n    } else {\n      console.warn(\n        `[AsyncStorage] The value for key \"${usedKey}\" is not a string. This can lead to unexpected behavior/errors. Consider stringifying it.\\nPassed value: ${value}\\nPassed key: ${usedKey}\\n`,\n      );\n    }\n  }\n}\n\n/**\n * `AsyncStorage` is a simple, unencrypted, asynchronous, persistent, key-value\n * storage system that is global to the app.  It should be used instead of\n * LocalStorage.\n *\n * See http://reactnative.dev/docs/asyncstorage.html\n */\nconst AsyncStorage = {\n  _getRequests: ([]: Array<MultiRequest>),\n  _getKeys: ([]: Array<string>),\n  _immediate: (null: ?number),\n\n  /**\n   * Fetches an item for a `key` and invokes a callback upon completion.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#getitem\n   */\n  getItem: function(\n    key: string,\n    callback?: ?(error: ?Error, result: string | null) => void,\n  ): Promise<string | null> {\n    return new Promise((resolve, reject) => {\n      checkValidInput(key);\n      RCTAsyncStorage.multiGet([key], function(errors, result) {\n        // Unpack result to get value from [[key,value]]\n        const value = result && result[0] && result[0][1] ? result[0][1] : null;\n        const errs = convertErrors(errors);\n        callback && callback(errs && errs[0], value);\n        if (errs) {\n          reject(errs[0]);\n        } else {\n          resolve(value);\n        }\n      });\n    });\n  },\n\n  /**\n   * Sets the value for a `key` and invokes a callback upon completion.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#setitem\n   */\n  setItem: function(\n    key: string,\n    value: string,\n    callback?: ?(error: ?Error) => void,\n  ): Promise<null> {\n    return new Promise((resolve, reject) => {\n      checkValidInput(key, value);\n      RCTAsyncStorage.multiSet([[key, value]], function(errors) {\n        const errs = convertErrors(errors);\n        callback && callback(errs && errs[0]);\n        if (errs) {\n          reject(errs[0]);\n        } else {\n          resolve(null);\n        }\n      });\n    });\n  },\n\n  /**\n   * Removes an item for a `key` and invokes a callback upon completion.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#removeitem\n   */\n  removeItem: function(\n    key: string,\n    callback?: ?(error: ?Error) => void,\n  ): Promise<null> {\n    return new Promise((resolve, reject) => {\n      checkValidInput(key);\n      RCTAsyncStorage.multiRemove([key], function(errors) {\n        const errs = convertErrors(errors);\n        callback && callback(errs && errs[0]);\n        if (errs) {\n          reject(errs[0]);\n        } else {\n          resolve(null);\n        }\n      });\n    });\n  },\n\n  /**\n   * Merges an existing `key` value with an input value, assuming both values\n   * are stringified JSON.\n   *\n   * **NOTE:** This is not supported by all native implementations.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#mergeitem\n   */\n  mergeItem: function(\n    key: string,\n    value: string,\n    callback?: ?(error: ?Error) => void,\n  ): Promise<null> {\n    return new Promise((resolve, reject) => {\n      checkValidInput(key, value);\n      RCTAsyncStorage.multiMerge([[key, value]], function(errors) {\n        const errs = convertErrors(errors);\n        callback && callback(errs && errs[0]);\n        if (errs) {\n          reject(errs[0]);\n        } else {\n          resolve(null);\n        }\n      });\n    });\n  },\n\n  /**\n   * Erases *all* `AsyncStorage` for all clients, libraries, etc. You probably\n   * don't want to call this; use `removeItem` or `multiRemove` to clear only\n   * your app's keys.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#clear\n   */\n  clear: function(callback?: ?(error: ?Error) => void): Promise<null> {\n    return new Promise((resolve, reject) => {\n      RCTAsyncStorage.clear(function(error) {\n        const err = convertError(error);\n        callback && callback(err);\n        if (err) {\n          reject(err);\n        } else {\n          resolve(null);\n        }\n      });\n    });\n  },\n\n  /**\n   * Gets *all* keys known to your app; for all callers, libraries, etc.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#getallkeys\n   */\n  getAllKeys: function(\n    callback?: ?(error: ?Error, keys: ?ReadOnlyArrayString) => void,\n  ): Promise<ReadOnlyArrayString> {\n    return new Promise((resolve, reject) => {\n      RCTAsyncStorage.getAllKeys(function(error, keys) {\n        const err = convertError(error);\n        callback && callback(err, keys);\n        if (err) {\n          reject(err);\n        } else {\n          resolve(keys);\n        }\n      });\n    });\n  },\n\n  /**\n   * The following batched functions are useful for executing a lot of\n   * operations at once, allowing for native optimizations and provide the\n   * convenience of a single callback after all operations are complete.\n   *\n   * These functions return arrays of errors, potentially one for every key.\n   * For key-specific errors, the Error object will have a key property to\n   * indicate which key caused the error.\n   */\n\n  /**\n   * Flushes any pending requests using a single batch call to get the data.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#flushgetrequests\n   * */\n  flushGetRequests: function(): void {\n    const getRequests = this._getRequests;\n    const getKeys = this._getKeys;\n\n    this._getRequests = [];\n    this._getKeys = [];\n\n    RCTAsyncStorage.multiGet(getKeys, function(errors, result) {\n      // Even though the runtime complexity of this is theoretically worse vs if we used a map,\n      // it's much, much faster in practice for the data sets we deal with (we avoid\n      // allocating result pair arrays). This was heavily benchmarked.\n      //\n      // Is there a way to avoid using the map but fix the bug in this breaking test?\n      // https://github.com/facebook/react-native/commit/8dd8ad76579d7feef34c014d387bf02065692264\n      const map = {};\n      result &&\n        result.forEach(([key, value]) => {\n          map[key] = value;\n          return value;\n        });\n      const reqLength = getRequests.length;\n      for (let i = 0; i < reqLength; i++) {\n        const request = getRequests[i];\n        const requestKeys = request.keys;\n        const requestResult = requestKeys.map(key => [key, map[key]]);\n        request.callback && request.callback(null, requestResult);\n        request.resolve && request.resolve(requestResult);\n      }\n    });\n  },\n\n  /**\n   * This allows you to batch the fetching of items given an array of `key`\n   * inputs. Your callback will be invoked with an array of corresponding\n   * key-value pairs found.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#multiget\n   */\n  multiGet: function(\n    keys: Array<string>,\n    callback?: ?MultiGetCallbackFunction,\n  ): Promise<?$ReadOnlyArray<ReadOnlyArrayString>> {\n    if (!this._immediate) {\n      this._immediate = setImmediate(() => {\n        this._immediate = null;\n        this.flushGetRequests();\n      });\n    }\n\n    const getRequest: MultiRequest = {\n      keys: keys,\n      callback: callback,\n      // do we need this?\n      keyIndex: this._getKeys.length,\n      resolve: null,\n      reject: null,\n    };\n\n    const promiseResult = new Promise((resolve, reject) => {\n      getRequest.resolve = resolve;\n      getRequest.reject = reject;\n    });\n\n    this._getRequests.push(getRequest);\n    // avoid fetching duplicates\n    keys.forEach(key => {\n      if (this._getKeys.indexOf(key) === -1) {\n        this._getKeys.push(key);\n      }\n    });\n\n    return promiseResult;\n  },\n\n  /**\n   * Use this as a batch operation for storing multiple key-value pairs. When\n   * the operation completes you'll get a single callback with any errors.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#multiset\n   */\n  multiSet: function(\n    keyValuePairs: Array<Array<string>>,\n    callback?: ?(errors: ?$ReadOnlyArray<?Error>) => void,\n  ): Promise<null> {\n    return new Promise((resolve, reject) => {\n      keyValuePairs.forEach(([key, value]) => {\n        checkValidInput(key, value);\n      });\n\n      RCTAsyncStorage.multiSet(keyValuePairs, function(errors) {\n        const error = convertErrors(errors);\n        callback && callback(error);\n        if (error) {\n          reject(error);\n        } else {\n          resolve(null);\n        }\n      });\n    });\n  },\n\n  /**\n   * Call this to batch the deletion of all keys in the `keys` array.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#multiremove\n   */\n  multiRemove: function(\n    keys: Array<string>,\n    callback?: ?(errors: ?$ReadOnlyArray<?Error>) => void,\n  ): Promise<null> {\n    return new Promise((resolve, reject) => {\n      keys.forEach(key => checkValidInput(key));\n\n      RCTAsyncStorage.multiRemove(keys, function(errors) {\n        const error = convertErrors(errors);\n        callback && callback(error);\n        if (error) {\n          reject(error);\n        } else {\n          resolve(null);\n        }\n      });\n    });\n  },\n\n  /**\n   * Batch operation to merge in existing and new values for a given set of\n   * keys. This assumes that the values are stringified JSON.\n   *\n   * **NOTE**: This is not supported by all native implementations.\n   *\n   * See http://reactnative.dev/docs/asyncstorage.html#multimerge\n   */\n  multiMerge: function(\n    keyValuePairs: Array<Array<string>>,\n    callback?: ?(errors: ?$ReadOnlyArray<?Error>) => void,\n  ): Promise<null> {\n    return new Promise((resolve, reject) => {\n      RCTAsyncStorage.multiMerge(keyValuePairs, function(errors) {\n        const error = convertErrors(errors);\n        callback && callback(error);\n        if (error) {\n          reject(error);\n        } else {\n          resolve(null);\n        }\n      });\n    });\n  },\n};\n\n// Not all native implementations support merge.\nif (!RCTAsyncStorage.multiMerge) {\n  delete AsyncStorage.mergeItem;\n  delete AsyncStorage.multiMerge;\n}\n\nfunction convertErrors(errs): ?$ReadOnlyArray<?Error> {\n  if (!errs || (Array.isArray(errs) && errs.length === 0)) {\n    return null;\n  }\n  return (Array.isArray(errs) ? errs : [errs]).map(e => convertError(e));\n}\n\nfunction convertError(error): ?Error {\n  if (!error) {\n    return null;\n  }\n  const out = new Error(error.message);\n  // $FlowFixMe: adding custom properties to error.\n  out.key = error.key;\n  return out;\n}\n\nexport default AsyncStorage;\n"]}