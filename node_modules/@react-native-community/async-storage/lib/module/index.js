var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});Object.defineProperty(exports,"useAsyncStorage",{enumerable:true,get:function get(){return _hooks.useAsyncStorage;}});exports.default=void 0;var _AsyncStorage=_interopRequireDefault(require("./AsyncStorage"));var _hooks=require("./hooks");var _default=_AsyncStorage.default;exports.default=_default;
//# sourceMappingURL=index.js.map