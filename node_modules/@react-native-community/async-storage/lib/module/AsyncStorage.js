var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;var _classCallCheck2=_interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));var _createClass2=_interopRequireDefault(require("@babel/runtime/helpers/createClass"));var _deepAssign=_interopRequireDefault(require("deep-assign"));var mergeLocalStorageItem=function mergeLocalStorageItem(key,value){var oldValue=window.localStorage.getItem(key);var oldObject=JSON.parse(oldValue);var newObject=JSON.parse(value);var nextValue=JSON.stringify((0,_deepAssign.default)({},oldObject,newObject));window.localStorage.setItem(key,nextValue);};var createPromise=function createPromise(getValue,callback){return new Promise(function(resolve,reject){try{var value=getValue();if(callback){callback(null,value);}resolve(value);}catch(err){if(callback){callback(err);}reject(err);}});};var createPromiseAll=function createPromiseAll(promises,callback,processResult){return Promise.all(promises).then(function(result){var value=processResult?processResult(result):null;callback&&callback(null,value);return Promise.resolve(value);},function(errors){callback&&callback(errors);return Promise.reject(errors);});};var AsyncStorage=function(){function AsyncStorage(){(0,_classCallCheck2.default)(this,AsyncStorage);}(0,_createClass2.default)(AsyncStorage,null,[{key:"getItem",value:function getItem(key,callback){return createPromise(function(){return window.localStorage.getItem(key);},callback);}},{key:"setItem",value:function setItem(key,value,callback){return createPromise(function(){window.localStorage.setItem(key,value);},callback);}},{key:"removeItem",value:function removeItem(key,callback){return createPromise(function(){return window.localStorage.removeItem(key);},callback);}},{key:"mergeItem",value:function mergeItem(key,value,callback){return createPromise(function(){mergeLocalStorageItem(key,value);},callback);}},{key:"clear",value:function clear(callback){return createPromise(function(){window.localStorage.clear();},callback);}},{key:"getAllKeys",value:function getAllKeys(callback){return createPromise(function(){var numberOfKeys=window.localStorage.length;var keys=[];for(var i=0;i<numberOfKeys;i+=1){var key=window.localStorage.key(i);keys.push(key);}return keys;},callback);}},{key:"flushGetRequests",value:function flushGetRequests(){}},{key:"multiGet",value:function multiGet(keys,callback){var promises=keys.map(function(key){return AsyncStorage.getItem(key);});var processResult=function processResult(result){return result.map(function(value,i){return[keys[i],value];});};return createPromiseAll(promises,callback,processResult);}},{key:"multiSet",value:function multiSet(keyValuePairs,callback){var promises=keyValuePairs.map(function(item){return AsyncStorage.setItem(item[0],item[1]);});return createPromiseAll(promises,callback);}},{key:"multiRemove",value:function multiRemove(keys,callback){var promises=keys.map(function(key){return AsyncStorage.removeItem(key);});return createPromiseAll(promises,callback);}},{key:"multiMerge",value:function multiMerge(keyValuePairs,callback){var promises=keyValuePairs.map(function(item){return AsyncStorage.mergeItem(item[0],item[1]);});return createPromiseAll(promises,callback);}}]);return AsyncStorage;}();exports.default=AsyncStorage;
//# sourceMappingURL=AsyncStorage.js.map