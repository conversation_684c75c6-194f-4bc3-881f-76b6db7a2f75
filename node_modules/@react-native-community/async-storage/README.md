# React Native Async Storage

An asynchronous, unencrypted, persistent, key-value storage system for React Native.


## Supported platforms

- iOS
- Android
- [Web](https://github.com/react-native-community/async-storage/releases/tag/v1.9.0)
- [MacOS](https://github.com/react-native-community/async-storage/releases/tag/v1.8.1)
- [Windows](https://github.com/react-native-community/async-storage/releases/tag/v1.10.0)


## Getting Started

Head over to [documentation](https://react-native-community.github.io/async-storage/) to learn more.


## Contribution
Pull requests are welcome. Please open an issue first to discuss what you would like to change.

See the [CONTRIBUTING](CONTRIBUTING.md) file for more information.

## License

MIT.
