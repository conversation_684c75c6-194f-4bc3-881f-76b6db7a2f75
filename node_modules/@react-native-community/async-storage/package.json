{"name": "@react-native-community/async-storage", "version": "1.12.0", "description": "Asynchronous, persistent, key-value storage system for React Native.", "main": "lib/commonjs/index.js", "module": "lib/module/index.js", "react-native": "src/index.js", "types": "./types/index.d.ts", "files": ["lib/", "src/", "jest/", "types/", "android/src", "android/build.gradle", "ios/", "macos/", "windows/", "RNCAsyncStorage.podspec"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/evanbacon)"], "homepage": "https://github.com/react-native-community/react-native-async-storage#readme", "license": "MIT", "keywords": ["react-native", "react native", "async storage", "asyncstorage", "storage"], "repository": {"type": "git", "url": "https://github.com/react-native-community/react-native-async-storage.git"}, "scripts": {"prepare": "bob build", "start": "node node_modules/react-native/local-cli/cli.js start", "start:android": "react-native run-android --root example/", "start:ios": "react-native run-ios --project-path example/ios --scheme AsyncStorageExample", "start:web": "expo start:web", "start:macos": "node node_modules/react-native-macos/local-cli/cli.js start --use-react-native-macos", "start:windows": "react-native start --use-react-native-windows", "build:e2e:ios": "detox build -c ios", "build:e2e:android": "detox build -c android", "build:e2e:macos": "scripts/run_macos_e2e.sh 'build'", "test": "yarn test:lint && yarn test:flow", "test:flow": "flow check", "test:lint": "eslint src/**/*.js example/**/*.js jest/*.js", "test:e2e:ios": "detox test -c ios", "test:e2e:android": "detox test -c android", "test:e2e:macos": "scripts/run_macos_e2e.sh 'test'"}, "peerDependencies": {"react": "^16.8", "react-native": ">=0.59"}, "dependencies": {"deep-assign": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.6.2", "@babel/runtime": "^7.6.2", "@react-native-community/bob": "^0.14.0", "@react-native-community/cli": "^3.1.0", "@react-native-community/cli-platform-android": "^3.1.0", "@react-native-community/cli-platform-ios": "^3.1.0", "@react-native-community/eslint-config": "^0.0.5", "babel-jest": "^24.9.0", "babel-plugin-module-resolver": "3.1.3", "detox": "12.6.1", "eslint": "5.1.0", "expo": "36.0.2", "flow-bin": "0.105.2", "jest": "^24.9.0", "metro": "0.56.4", "metro-react-native-babel-preset": "^0.56.0", "react": "16.9.0", "react-dom": "16.9.0", "react-native": "0.61.5", "react-native-macos": "0.60.0-microsoft.50", "react-native-web": "~0.12.0", "react-native-windows": "0.61.0", "react-test-renderer": "16.9.0"}, "jest": {"preset": "react-native", "setupFiles": ["./example/jest.setup.js"]}, "detox": {"test-runner": "jest", "runner-config": "example/e2e/config.json", "configurations": {"ios": {"binaryPath": "example/ios/build/Build/Products/Release-iphonesimulator/AsyncStorageExample.app", "build": ".circleci/scripts/run_ios_e2e.sh \"iPhone X\"", "type": "ios.simulator", "name": "iPhone X"}, "android": {"binaryPath": "example/android/app/build/outputs/apk/release/app-release.apk", "build": ".circleci/scripts/run_android_e2e.sh", "type": "android.emulator", "name": "Emu_E2E"}}}, "@react-native-community/bob": {"source": "src", "output": "lib", "targets": [["commonjs", {"copyFlow": true}], "module"]}}