def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

buildscript {
    // The Android Gradle plugin is only required when opening the android folder stand-alone.
    // This avoids unnecessary downloads and potential conflicts when the library is included as a
    // module dependency in an application project.
    if (project == rootProject) {
        repositories {
            google()
            jcenter()
        }
        dependencies {
            classpath 'com.android.tools.build:gradle:3.5.0'
        }
    }
}

// AsyncStorage has default size of 6MB.
// This is a sane limit to protect the user from the app storing too much data in the database.
// This also protects the database from filling up the disk cache and becoming malformed.
// If you really need bigger size, please keep in mind the potential consequences.
long dbSizeInMB = 6L

def newDbSize = rootProject.properties['AsyncStorage_db_size_in_MB']

if( newDbSize != null && newDbSize.isLong()) {
    dbSizeInMB = newDbSize.toLong()
}

def useDedicatedExecutor = rootProject.hasProperty('AsyncStorage_dedicatedExecutor')
        ? rootProject.properties['AsyncStorage_dedicatedExecutor']
        : false

apply plugin: 'com.android.library'

android {
    compileSdkVersion safeExtGet('compileSdkVersion', 28)
    buildToolsVersion safeExtGet('buildToolsVersion', '28.0.3')
    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', 19)
        targetSdkVersion safeExtGet('targetSdkVersion', 28)
        buildConfigField "Long", "AsyncStorage_db_size", "${dbSizeInMB}L"
        buildConfigField("boolean", "AsyncStorage_useDedicatedExecutor", "${useDedicatedExecutor}")
    }
    lintOptions {
        abortOnError false
    }
}

repositories {
    mavenLocal()
    maven {
        // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
        url "$rootDir/../node_modules/react-native/android"
    }
    google()
    jcenter()
}

dependencies {
    //noinspection GradleDynamicVersion
    implementation 'com.facebook.react:react-native:+'  // From node_modules
}
