{"version": 3, "names": ["getPackageJson", "root", "require", "path", "join", "CLIError", "getPlatformDependencies", "dependencies", "platformName", "Object", "keys", "filter", "dependency", "platforms", "map", "version", "sort", "dependenciesToString", "generateMd5Hash", "text", "createHash", "update", "digest", "compareMd5Hashes", "hash1", "hash2", "install", "packageJson", "cachedDependenciesHash", "currentDependenciesHash", "iosFolderPath", "loader", "<PERSON><PERSON><PERSON><PERSON>", "installPods", "skipBundleInstall", "cacheManager", "set", "name", "succeed", "fail", "chalk", "bold", "resolvePods", "nativeDependencies", "options", "podfilePath", "findPodfilePath", "platformFolderPath", "slice", "lastIndexOf", "podsPath", "arePodsInstalled", "fs", "existsSync", "platformDependencies", "dependenciesString", "get", "forceInstall", "undefined", "newArchEnabled"], "sources": ["../../src/tools/pods.ts"], "sourcesContent": ["import path from 'path';\nimport fs from 'fs-extra';\nimport {createHash} from 'crypto';\nimport chalk from 'chalk';\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  cache<PERSON>anager,\n  getLoader,\n} from '@react-native-community/cli-tools';\nimport installPods from './installPods';\nimport findPodfilePath from '../config/findPodfilePath';\nimport {\n  DependencyConfig,\n  IOSDependencyConfig,\n} from '@react-native-community/cli-types';\nimport {ApplePlatform} from '../types';\n\ninterface ResolvePodsOptions {\n  forceInstall?: boolean;\n  newArchEnabled?: boolean;\n}\n\ninterface NativeDependencies {\n  [key: string]: DependencyConfig;\n}\n\nfunction getPackageJson(root: string) {\n  try {\n    return require(path.join(root, 'package.json'));\n  } catch {\n    throw new CLIError(\n      'No package.json found. Please make sure the file exists in the current folder.',\n    );\n  }\n}\n\nexport function getPlatformDependencies(\n  dependencies: NativeDependencies,\n  platformName: ApplePlatform,\n) {\n  return Object.keys(dependencies)\n    .filter((dependency) => dependencies[dependency].platforms?.[platformName])\n    .map(\n      (dependency) =>\n        `${dependency}@${\n          (\n            dependencies[dependency].platforms?.[\n              platformName\n            ] as IOSDependencyConfig\n          ).version\n        }`,\n    )\n    .sort();\n}\n\nexport function dependenciesToString(dependencies: string[]) {\n  return dependencies.join('\\n');\n}\n\nexport function generateMd5Hash(text: string) {\n  return createHash('md5').update(text).digest('hex');\n}\n\nexport function compareMd5Hashes(hash1: string, hash2: string) {\n  return hash1 === hash2;\n}\n\nasync function install(\n  packageJson: Record<string, any>,\n  cachedDependenciesHash: string | undefined,\n  currentDependenciesHash: string,\n  iosFolderPath: string,\n) {\n  const loader = getLoader('Installing CocoaPods...');\n  try {\n    await installPods(loader, {\n      skipBundleInstall: !!cachedDependenciesHash,\n      iosFolderPath,\n    });\n    cacheManager.set(packageJson.name, 'dependencies', currentDependenciesHash);\n    loader.succeed();\n  } catch {\n    loader.fail();\n    throw new CLIError(\n      `Something when wrong while installing CocoaPods. Please run ${chalk.bold(\n        'pod install',\n      )} manually`,\n    );\n  }\n}\n\nexport default async function resolvePods(\n  root: string,\n  nativeDependencies: NativeDependencies,\n  platformName: ApplePlatform,\n  options?: ResolvePodsOptions,\n) {\n  const packageJson = getPackageJson(root);\n  const podfilePath = findPodfilePath(root, platformName);\n  const platformFolderPath = podfilePath\n    ? podfilePath.slice(0, podfilePath.lastIndexOf('/'))\n    : path.join(root, platformName);\n  const podsPath = path.join(platformFolderPath, 'Pods');\n  const arePodsInstalled = fs.existsSync(podsPath);\n  const platformDependencies = getPlatformDependencies(\n    nativeDependencies,\n    platformName,\n  );\n  const dependenciesString = dependenciesToString(platformDependencies);\n  const currentDependenciesHash = generateMd5Hash(dependenciesString);\n  const cachedDependenciesHash = cacheManager.get(\n    packageJson.name,\n    'dependencies',\n  );\n\n  if (options?.forceInstall) {\n    await install(\n      packageJson,\n      cachedDependenciesHash,\n      currentDependenciesHash,\n      platformFolderPath,\n    );\n  } else if (arePodsInstalled && cachedDependenciesHash === undefined) {\n    cacheManager.set(packageJson.name, 'dependencies', currentDependenciesHash);\n  } else if (\n    !cachedDependenciesHash ||\n    !compareMd5Hashes(currentDependenciesHash, cachedDependenciesHash) ||\n    !arePodsInstalled\n  ) {\n    const loader = getLoader('Installing CocoaPods...');\n    try {\n      await installPods(loader, {\n        skipBundleInstall: !!cachedDependenciesHash,\n        newArchEnabled: options?.newArchEnabled,\n        iosFolderPath: platformFolderPath,\n      });\n      cacheManager.set(\n        packageJson.name,\n        'dependencies',\n        currentDependenciesHash,\n      );\n      loader.succeed();\n    } catch {\n      loader.fail();\n      throw new CLIError(\n        `Something when wrong while installing CocoaPods. Please run ${chalk.bold(\n          'pod install',\n        )} manually`,\n      );\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAKA;AACA;AAAwD;AAgBxD,SAASA,cAAc,CAACC,IAAY,EAAE;EACpC,IAAI;IACF,OAAOC,OAAO,CAACC,eAAI,CAACC,IAAI,CAACH,IAAI,EAAE,cAAc,CAAC,CAAC;EACjD,CAAC,CAAC,MAAM;IACN,MAAM,KAAII,oBAAQ,EAChB,gFAAgF,CACjF;EACH;AACF;AAEO,SAASC,uBAAuB,CACrCC,YAAgC,EAChCC,YAA2B,EAC3B;EACA,OAAOC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC,CAC7BI,MAAM,CAAEC,UAAU;IAAA;IAAA,gCAAKL,YAAY,CAACK,UAAU,CAAC,CAACC,SAAS,0DAAlC,sBAAqCL,YAAY,CAAC;EAAA,EAAC,CAC1EM,GAAG,CACDF,UAAU;IAAA;IAAA,OACR,GAAEA,UAAW,IACZ,2BACEL,YAAY,CAACK,UAAU,CAAC,CAACC,SAAS,2DAAlC,uBACEL,YAAY,CACb,EACDO,OACH,EAAC;EAAA,EACL,CACAC,IAAI,EAAE;AACX;AAEO,SAASC,oBAAoB,CAACV,YAAsB,EAAE;EAC3D,OAAOA,YAAY,CAACH,IAAI,CAAC,IAAI,CAAC;AAChC;AAEO,SAASc,eAAe,CAACC,IAAY,EAAE;EAC5C,OAAO,IAAAC,oBAAU,EAAC,KAAK,CAAC,CAACC,MAAM,CAACF,IAAI,CAAC,CAACG,MAAM,CAAC,KAAK,CAAC;AACrD;AAEO,SAASC,gBAAgB,CAACC,KAAa,EAAEC,KAAa,EAAE;EAC7D,OAAOD,KAAK,KAAKC,KAAK;AACxB;AAEA,eAAeC,OAAO,CACpBC,WAAgC,EAChCC,sBAA0C,EAC1CC,uBAA+B,EAC/BC,aAAqB,EACrB;EACA,MAAMC,MAAM,GAAG,IAAAC,qBAAS,EAAC,yBAAyB,CAAC;EACnD,IAAI;IACF,MAAM,IAAAC,oBAAW,EAACF,MAAM,EAAE;MACxBG,iBAAiB,EAAE,CAAC,CAACN,sBAAsB;MAC3CE;IACF,CAAC,CAAC;IACFK,wBAAY,CAACC,GAAG,CAACT,WAAW,CAACU,IAAI,EAAE,cAAc,EAAER,uBAAuB,CAAC;IAC3EE,MAAM,CAACO,OAAO,EAAE;EAClB,CAAC,CAAC,MAAM;IACNP,MAAM,CAACQ,IAAI,EAAE;IACb,MAAM,KAAIlC,oBAAQ,EACf,+DAA8DmC,gBAAK,CAACC,IAAI,CACvE,aAAa,CACb,WAAU,CACb;EACH;AACF;AAEe,eAAeC,WAAW,CACvCzC,IAAY,EACZ0C,kBAAsC,EACtCnC,YAA2B,EAC3BoC,OAA4B,EAC5B;EACA,MAAMjB,WAAW,GAAG3B,cAAc,CAACC,IAAI,CAAC;EACxC,MAAM4C,WAAW,GAAG,IAAAC,wBAAe,EAAC7C,IAAI,EAAEO,YAAY,CAAC;EACvD,MAAMuC,kBAAkB,GAAGF,WAAW,GAClCA,WAAW,CAACG,KAAK,CAAC,CAAC,EAAEH,WAAW,CAACI,WAAW,CAAC,GAAG,CAAC,CAAC,GAClD9C,eAAI,CAACC,IAAI,CAACH,IAAI,EAAEO,YAAY,CAAC;EACjC,MAAM0C,QAAQ,GAAG/C,eAAI,CAACC,IAAI,CAAC2C,kBAAkB,EAAE,MAAM,CAAC;EACtD,MAAMI,gBAAgB,GAAGC,kBAAE,CAACC,UAAU,CAACH,QAAQ,CAAC;EAChD,MAAMI,oBAAoB,GAAGhD,uBAAuB,CAClDqC,kBAAkB,EAClBnC,YAAY,CACb;EACD,MAAM+C,kBAAkB,GAAGtC,oBAAoB,CAACqC,oBAAoB,CAAC;EACrE,MAAMzB,uBAAuB,GAAGX,eAAe,CAACqC,kBAAkB,CAAC;EACnE,MAAM3B,sBAAsB,GAAGO,wBAAY,CAACqB,GAAG,CAC7C7B,WAAW,CAACU,IAAI,EAChB,cAAc,CACf;EAED,IAAIO,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEa,YAAY,EAAE;IACzB,MAAM/B,OAAO,CACXC,WAAW,EACXC,sBAAsB,EACtBC,uBAAuB,EACvBkB,kBAAkB,CACnB;EACH,CAAC,MAAM,IAAII,gBAAgB,IAAIvB,sBAAsB,KAAK8B,SAAS,EAAE;IACnEvB,wBAAY,CAACC,GAAG,CAACT,WAAW,CAACU,IAAI,EAAE,cAAc,EAAER,uBAAuB,CAAC;EAC7E,CAAC,MAAM,IACL,CAACD,sBAAsB,IACvB,CAACL,gBAAgB,CAACM,uBAAuB,EAAED,sBAAsB,CAAC,IAClE,CAACuB,gBAAgB,EACjB;IACA,MAAMpB,MAAM,GAAG,IAAAC,qBAAS,EAAC,yBAAyB,CAAC;IACnD,IAAI;MACF,MAAM,IAAAC,oBAAW,EAACF,MAAM,EAAE;QACxBG,iBAAiB,EAAE,CAAC,CAACN,sBAAsB;QAC3C+B,cAAc,EAAEf,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEe,cAAc;QACvC7B,aAAa,EAAEiB;MACjB,CAAC,CAAC;MACFZ,wBAAY,CAACC,GAAG,CACdT,WAAW,CAACU,IAAI,EAChB,cAAc,EACdR,uBAAuB,CACxB;MACDE,MAAM,CAACO,OAAO,EAAE;IAClB,CAAC,CAAC,MAAM;MACNP,MAAM,CAACQ,IAAI,EAAE;MACb,MAAM,KAAIlC,oBAAQ,EACf,+DAA8DmC,gBAAK,CAACC,IAAI,CACvE,aAAa,CACb,WAAU,CACb;IACH;EACF;AACF"}