{"version": 3, "names": ["runPodInstall", "loader", "options", "shouldHandleRepoUpdate", "start", "chalk", "bold", "newArchEnabled", "dim", "execa", "env", "RCT_NEW_ARCH_ENABLED", "error", "logger", "debug", "stderr", "stdout", "includes", "runPodUpdate", "fail", "CLIError", "link", "docs", "guide", "log", "underline", "installCocoaPodsWithGem", "_error", "runSudo", "join", "installCocoaPods", "stop", "succeed", "installPods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "iosFolderPath", "fs", "existsSync", "process", "chdir", "hasPods", "skipBundleInstall", "runBundleInstall", "e", "info"], "sources": ["../../src/tools/installPods.ts"], "sourcesContent": ["import fs from 'fs';\nimport execa from 'execa';\nimport type {Ora} from 'ora';\nimport chalk from 'chalk';\nimport {\n  logger,\n  NoopLoader,\n  link,\n  CLIError,\n  runSudo,\n} from '@react-native-community/cli-tools';\nimport runBundleInstall from './runBundleInstall';\n\ninterface PodInstallOptions {\n  skipBundleInstall?: boolean;\n  newArchEnabled?: boolean;\n  iosFolderPath?: string;\n}\n\ninterface RunPodInstallOptions {\n  shouldHandleRepoUpdate?: boolean;\n  newArchEnabled?: boolean;\n}\n\nasync function runPodInstall(loader: Ora, options?: RunPodInstallOptions) {\n  const shouldHandleRepoUpdate = options?.shouldHandleRepoUpdate || true;\n  try {\n    loader.start(\n      `Installing CocoaPods dependencies ${chalk.bold(\n        options?.newArchEnabled ? 'with New Architecture' : '',\n      )} ${chalk.dim('(this may take a few minutes)')}`,\n    );\n\n    await execa('bundle', ['exec', 'pod', 'install'], {\n      env: {\n        RCT_NEW_ARCH_ENABLED: options?.newArchEnabled ? '1' : '0',\n      },\n    });\n  } catch (error) {\n    logger.debug(error as string);\n    // \"pod\" command outputs errors to stdout (at least some of them)\n    const stderr = (error as any).stderr || (error as any).stdout;\n\n    /**\n     * If CocoaPods failed due to repo being out of date, it will\n     * include the update command in the error message.\n     *\n     * `shouldHandleRepoUpdate` will be set to `false` to\n     * prevent infinite loop (unlikely scenario)\n     */\n    if (stderr.includes('pod repo update') && shouldHandleRepoUpdate) {\n      await runPodUpdate(loader);\n      await runPodInstall(loader, {\n        shouldHandleRepoUpdate: false,\n        newArchEnabled: options?.newArchEnabled,\n      });\n    } else {\n      loader.fail();\n      logger.error(stderr);\n\n      throw new CLIError(\n        `Looks like your iOS environment is not properly set. Please go to ${link.docs(\n          'environment-setup',\n          'ios',\n          {guide: 'native'},\n        )} and follow the React Native CLI QuickStart guide for macOS and iOS.`,\n      );\n    }\n  }\n}\n\nasync function runPodUpdate(loader: Ora) {\n  try {\n    loader.start(\n      `Updating CocoaPods repositories ${chalk.dim(\n        '(this may take a few minutes)',\n      )}`,\n    );\n    await execa('pod', ['repo', 'update']);\n  } catch (error) {\n    // \"pod\" command outputs errors to stdout (at least some of them)\n    logger.log((error as any).stderr || (error as any).stdout);\n    loader.fail();\n\n    throw new CLIError(\n      `Failed to update CocoaPods repositories for iOS project.\\nPlease try again manually: \"pod repo update\".\\nCocoaPods documentation: ${chalk.dim.underline(\n        'https://cocoapods.org/',\n      )}`,\n    );\n  }\n}\n\nasync function installCocoaPodsWithGem() {\n  const options = ['install', 'cocoapods', '--no-document'];\n\n  try {\n    // First attempt to install `cocoapods`\n    await execa('gem', options);\n  } catch (_error) {\n    // If that doesn't work then try with sudo\n    await runSudo(`gem ${options.join(' ')}`);\n  }\n}\n\nasync function installCocoaPods(loader: Ora) {\n  loader.stop();\n\n  loader.start('Installing CocoaPods');\n\n  try {\n    await installCocoaPodsWithGem();\n\n    return loader.succeed();\n  } catch (error) {\n    loader.fail();\n    logger.error((error as any).stderr);\n\n    throw new CLIError(\n      `An error occured while trying to install CocoaPods, which is required by this template.\\nPlease try again manually: sudo gem install cocoapods.\\nCocoaPods documentation: ${chalk.dim.underline(\n        'https://cocoapods.org/',\n      )}`,\n    );\n  }\n}\n\nasync function installPods(loader?: Ora, options?: PodInstallOptions) {\n  loader = loader || new NoopLoader();\n  try {\n    if (!options?.iosFolderPath && !fs.existsSync('ios')) {\n      return;\n    }\n\n    process.chdir(options?.iosFolderPath ?? 'ios');\n\n    const hasPods = fs.existsSync('Podfile');\n\n    if (!hasPods) {\n      return;\n    }\n\n    if (fs.existsSync('../Gemfile') && !options?.skipBundleInstall) {\n      await runBundleInstall(loader);\n    } else if (!fs.existsSync('../Gemfile')) {\n      throw new CLIError(\n        'Could not find the Gemfile. Currently the CLI requires to have this file in the root directory of the project to install CocoaPods. If your configuration is different, please install the CocoaPods manually.',\n      );\n    }\n\n    try {\n      // Check if \"pod\" is available and usable. It happens that there are\n      // multiple versions of \"pod\" command and even though it's there, it exits\n      // with a failure\n      await execa('pod', ['--version']);\n    } catch (e) {\n      loader.info();\n      await installCocoaPods(loader);\n    }\n\n    await runPodInstall(loader, {newArchEnabled: options?.newArchEnabled});\n  } finally {\n    process.chdir('..');\n  }\n}\n\nexport {installCocoaPods};\n\nexport default installPods;\n"], "mappings": ";;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAOA;AAAkD;AAalD,eAAeA,aAAa,CAACC,MAAW,EAAEC,OAA8B,EAAE;EACxE,MAAMC,sBAAsB,GAAG,CAAAD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,sBAAsB,KAAI,IAAI;EACtE,IAAI;IACFF,MAAM,CAACG,KAAK,CACT,qCAAoCC,gBAAK,CAACC,IAAI,CAC7C,CAAAJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,cAAc,IAAG,uBAAuB,GAAG,EAAE,CACtD,IAAGF,gBAAK,CAACG,GAAG,CAAC,+BAA+B,CAAE,EAAC,CAClD;IAED,MAAM,IAAAC,gBAAK,EAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE;MAChDC,GAAG,EAAE;QACHC,oBAAoB,EAAE,CAAAT,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,cAAc,IAAG,GAAG,GAAG;MACxD;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,kBAAM,CAACC,KAAK,CAACF,KAAK,CAAW;IAC7B;IACA,MAAMG,MAAM,GAAIH,KAAK,CAASG,MAAM,IAAKH,KAAK,CAASI,MAAM;;IAE7D;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAID,MAAM,CAACE,QAAQ,CAAC,iBAAiB,CAAC,IAAId,sBAAsB,EAAE;MAChE,MAAMe,YAAY,CAACjB,MAAM,CAAC;MAC1B,MAAMD,aAAa,CAACC,MAAM,EAAE;QAC1BE,sBAAsB,EAAE,KAAK;QAC7BI,cAAc,EAAEL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK;MAC3B,CAAC,CAAC;IACJ,CAAC,MAAM;MACLN,MAAM,CAACkB,IAAI,EAAE;MACbN,kBAAM,CAACD,KAAK,CAACG,MAAM,CAAC;MAEpB,MAAM,KAAIK,oBAAQ,EACf,qEAAoEC,gBAAI,CAACC,IAAI,CAC5E,mBAAmB,EACnB,KAAK,EACL;QAACC,KAAK,EAAE;MAAQ,CAAC,CACjB,sEAAqE,CACxE;IACH;EACF;AACF;AAEA,eAAeL,YAAY,CAACjB,MAAW,EAAE;EACvC,IAAI;IACFA,MAAM,CAACG,KAAK,CACT,mCAAkCC,gBAAK,CAACG,GAAG,CAC1C,+BAA+B,CAC/B,EAAC,CACJ;IACD,MAAM,IAAAC,gBAAK,EAAC,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;EACxC,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd;IACAC,kBAAM,CAACW,GAAG,CAAEZ,KAAK,CAASG,MAAM,IAAKH,KAAK,CAASI,MAAM,CAAC;IAC1Df,MAAM,CAACkB,IAAI,EAAE;IAEb,MAAM,KAAIC,oBAAQ,EACf,qIAAoIf,gBAAK,CAACG,GAAG,CAACiB,SAAS,CACtJ,wBAAwB,CACxB,EAAC,CACJ;EACH;AACF;AAEA,eAAeC,uBAAuB,GAAG;EACvC,MAAMxB,OAAO,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC;EAEzD,IAAI;IACF;IACA,MAAM,IAAAO,gBAAK,EAAC,KAAK,EAAEP,OAAO,CAAC;EAC7B,CAAC,CAAC,OAAOyB,MAAM,EAAE;IACf;IACA,MAAM,IAAAC,mBAAO,EAAE,OAAM1B,OAAO,CAAC2B,IAAI,CAAC,GAAG,CAAE,EAAC,CAAC;EAC3C;AACF;AAEA,eAAeC,gBAAgB,CAAC7B,MAAW,EAAE;EAC3CA,MAAM,CAAC8B,IAAI,EAAE;EAEb9B,MAAM,CAACG,KAAK,CAAC,sBAAsB,CAAC;EAEpC,IAAI;IACF,MAAMsB,uBAAuB,EAAE;IAE/B,OAAOzB,MAAM,CAAC+B,OAAO,EAAE;EACzB,CAAC,CAAC,OAAOpB,KAAK,EAAE;IACdX,MAAM,CAACkB,IAAI,EAAE;IACbN,kBAAM,CAACD,KAAK,CAAEA,KAAK,CAASG,MAAM,CAAC;IAEnC,MAAM,KAAIK,oBAAQ,EACf,6KAA4Kf,gBAAK,CAACG,GAAG,CAACiB,SAAS,CAC9L,wBAAwB,CACxB,EAAC,CACJ;EACH;AACF;AAEA,eAAeQ,WAAW,CAAChC,MAAY,EAAEC,OAA2B,EAAE;EACpED,MAAM,GAAGA,MAAM,IAAI,KAAIiC,sBAAU,GAAE;EACnC,IAAI;IACF,IAAI,EAAChC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiC,aAAa,KAAI,CAACC,aAAE,CAACC,UAAU,CAAC,KAAK,CAAC,EAAE;MACpD;IACF;IAEAC,OAAO,CAACC,KAAK,CAAC,CAAArC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEiC,aAAa,KAAI,KAAK,CAAC;IAE9C,MAAMK,OAAO,GAAGJ,aAAE,CAACC,UAAU,CAAC,SAAS,CAAC;IAExC,IAAI,CAACG,OAAO,EAAE;MACZ;IACF;IAEA,IAAIJ,aAAE,CAACC,UAAU,CAAC,YAAY,CAAC,IAAI,EAACnC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEuC,iBAAiB,GAAE;MAC9D,MAAM,IAAAC,yBAAgB,EAACzC,MAAM,CAAC;IAChC,CAAC,MAAM,IAAI,CAACmC,aAAE,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;MACvC,MAAM,KAAIjB,oBAAQ,EAChB,gNAAgN,CACjN;IACH;IAEA,IAAI;MACF;MACA;MACA;MACA,MAAM,IAAAX,gBAAK,EAAC,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC,CAAC,OAAOkC,CAAC,EAAE;MACV1C,MAAM,CAAC2C,IAAI,EAAE;MACb,MAAMd,gBAAgB,CAAC7B,MAAM,CAAC;IAChC;IAEA,MAAMD,aAAa,CAACC,MAAM,EAAE;MAACM,cAAc,EAAEL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK;IAAc,CAAC,CAAC;EACxE,CAAC,SAAS;IACR+B,OAAO,CAACC,KAAK,CAAC,IAAI,CAAC;EACrB;AACF;AAAC,eAIcN,WAAW;AAAA"}