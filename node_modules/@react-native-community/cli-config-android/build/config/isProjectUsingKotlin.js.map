{"version": 3, "names": ["isProjectUsingKotlin", "sourceDir", "mainActivityFiles", "getMainActivityFiles", "some", "file", "endsWith"], "sources": ["../../src/config/isProjectUsingKotlin.ts"], "sourcesContent": ["import {getMainActivityFiles} from './findPackageClassName';\n\nexport default function isProjectUsingKotlin(sourceDir: string): boolean {\n  const mainActivityFiles = getMainActivityFiles(sourceDir, false);\n\n  return mainActivityFiles.some((file) => file.endsWith('.kt'));\n}\n"], "mappings": ";;;;;;AAAA;AAEe,SAASA,oBAAoB,CAACC,SAAiB,EAAW;EACvE,MAAMC,iBAAiB,GAAG,IAAAC,0CAAoB,EAACF,SAAS,EAAE,KAAK,CAAC;EAEhE,OAAOC,iBAAiB,CAACE,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC/D"}