{"version": 3, "names": ["projectConfig", "root", "userConfig", "src", "sourceDir", "findAndroidDir", "path", "join", "appName", "getAppName", "manifestPath", "findManifest", "buildGradlePath", "findBuildGradle", "packageName", "getPackageName", "CLIError", "applicationId", "getApplicationId", "mainActivity", "getMainActivity", "dependencyConfiguration", "watchModeCommandParams", "assets", "appId", "parseApplicationIdFromBuildGradleFile", "userConfigAppName", "fs", "existsSync", "dependencyConfig", "isPureCxxDependency", "cxxModuleCMakeListsModuleName", "cxxModuleCMakeListsPath", "cxxModuleHeaderName", "packageImportPath", "packageInstance", "packageClassName", "findPackageClassName", "buildTypes", "libraryName", "findLibraryName", "componentDescriptors", "findComponentDescriptors", "cmakeListsPath", "process", "platform", "replace"], "sources": ["../../src/config/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport fs from 'fs';\nimport findAndroidDir from './findAndroidDir';\nimport findManifest from './findManifest';\nimport findPackageClassName from './findPackageClassName';\nimport {\n  AndroidProjectParams,\n  AndroidProjectConfig,\n  AndroidDependencyParams,\n  AndroidDependencyConfig,\n} from '@react-native-community/cli-types';\nimport {\n  getPackageName,\n  parseApplicationIdFromBuildGradleFile,\n} from './getAndroidProject';\nimport {findLibraryName} from './findLibraryName';\nimport {findComponentDescriptors} from './findComponentDescriptors';\nimport {findBuildGradle} from './findBuildGradle';\nimport {CLIError} from '@react-native-community/cli-tools';\nimport getMainActivity from './getMainActivity';\n\n/**\n * Gets android project config by analyzing given folder and taking some\n * defaults specified by user into consideration\n */\nexport function projectConfig(\n  root: string,\n  userConfig: AndroidProjectParams = {},\n): AndroidProjectConfig | null {\n  const src = userConfig.sourceDir || findAndroidDir(root);\n\n  if (!src) {\n    return null;\n  }\n\n  const sourceDir = path.join(root, src);\n\n  const appName = getAppName(sourceDir, userConfig.appName);\n\n  const manifestPath = userConfig.manifestPath\n    ? path.join(sourceDir, userConfig.manifestPath)\n    : findManifest(path.join(sourceDir, appName));\n  const buildGradlePath = findBuildGradle(sourceDir, false);\n\n  if (!manifestPath && !buildGradlePath) {\n    return null;\n  }\n\n  const packageName =\n    userConfig.packageName || getPackageName(manifestPath, buildGradlePath);\n\n  if (!packageName) {\n    throw new CLIError(\n      `Package name not found in neither ${manifestPath} nor ${buildGradlePath}`,\n    );\n  }\n\n  const applicationId = buildGradlePath\n    ? getApplicationId(buildGradlePath, packageName)\n    : packageName;\n  const mainActivity = getMainActivity(manifestPath || '') ?? '';\n\n  return {\n    sourceDir,\n    appName,\n    packageName,\n    applicationId,\n    mainActivity,\n    dependencyConfiguration: userConfig.dependencyConfiguration,\n    watchModeCommandParams: userConfig.watchModeCommandParams,\n    assets: userConfig.assets ?? [],\n  };\n}\n\nfunction getApplicationId(buildGradlePath: string, packageName: string) {\n  let appId = packageName;\n\n  const applicationId = parseApplicationIdFromBuildGradleFile(buildGradlePath);\n  if (applicationId) {\n    appId = applicationId;\n  }\n  return appId;\n}\n\nfunction getAppName(sourceDir: string, userConfigAppName: string | undefined) {\n  let appName = '';\n  if (\n    typeof userConfigAppName === 'string' &&\n    fs.existsSync(path.join(sourceDir, userConfigAppName))\n  ) {\n    appName = userConfigAppName;\n  } else if (fs.existsSync(path.join(sourceDir, 'app'))) {\n    appName = 'app';\n  }\n  return appName;\n}\n\n/**\n * Same as projectConfigAndroid except it returns\n * different config that applies to packages only\n */\nexport function dependencyConfig(\n  root: string,\n  userConfig: AndroidDependencyParams | null = {},\n): AndroidDependencyConfig | null {\n  if (userConfig === null) {\n    return null;\n  }\n\n  const src = userConfig.sourceDir || findAndroidDir(root);\n\n  if (!src) {\n    return null;\n  }\n\n  const sourceDir = path.join(root, src);\n  const manifestPath = userConfig.manifestPath\n    ? path.join(sourceDir, userConfig.manifestPath)\n    : findManifest(sourceDir);\n  const buildGradlePath = findBuildGradle(sourceDir, true);\n  const isPureCxxDependency =\n    userConfig.cxxModuleCMakeListsModuleName != null &&\n    userConfig.cxxModuleCMakeListsPath != null &&\n    userConfig.cxxModuleHeaderName != null &&\n    !manifestPath &&\n    !buildGradlePath;\n\n  if (!manifestPath && !buildGradlePath && !isPureCxxDependency) {\n    return null;\n  }\n\n  let packageImportPath = null,\n    packageInstance = null;\n\n  if (!isPureCxxDependency) {\n    const packageName =\n      userConfig.packageName || getPackageName(manifestPath, buildGradlePath);\n    const packageClassName = findPackageClassName(sourceDir);\n\n    /**\n     * This module has no package to export\n     */\n    if (!packageClassName) {\n      return null;\n    }\n\n    packageImportPath =\n      userConfig.packageImportPath ||\n      `import ${packageName}.${packageClassName};`;\n\n    packageInstance = userConfig.packageInstance || `new ${packageClassName}()`;\n  }\n\n  const buildTypes = userConfig.buildTypes || [];\n  const dependencyConfiguration = userConfig.dependencyConfiguration;\n  const libraryName =\n    userConfig.libraryName || findLibraryName(root, sourceDir);\n  const componentDescriptors =\n    userConfig.componentDescriptors || findComponentDescriptors(root);\n  let cmakeListsPath = userConfig.cmakeListsPath\n    ? path.join(sourceDir, userConfig.cmakeListsPath)\n    : path.join(sourceDir, 'build/generated/source/codegen/jni/CMakeLists.txt');\n  const cxxModuleCMakeListsModuleName =\n    userConfig.cxxModuleCMakeListsModuleName || null;\n  const cxxModuleHeaderName = userConfig.cxxModuleHeaderName || null;\n  let cxxModuleCMakeListsPath = userConfig.cxxModuleCMakeListsPath\n    ? path.join(sourceDir, userConfig.cxxModuleCMakeListsPath)\n    : null;\n\n  if (process.platform === 'win32') {\n    cmakeListsPath = cmakeListsPath.replace(/\\\\/g, '/');\n    if (cxxModuleCMakeListsPath) {\n      cxxModuleCMakeListsPath = cxxModuleCMakeListsPath.replace(/\\\\/g, '/');\n    }\n  }\n\n  return {\n    sourceDir,\n    packageImportPath,\n    packageInstance,\n    buildTypes,\n    dependencyConfiguration,\n    libraryName,\n    componentDescriptors,\n    cmakeListsPath,\n    cxxModuleCMakeListsModuleName,\n    cxxModuleCMakeListsPath,\n    cxxModuleHeaderName,\n    isPureCxxDependency,\n  };\n}\n"], "mappings": ";;;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AAOA;AAIA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAAgD;AA3BhD;AACA;AACA;AACA;AACA;AACA;AACA;;AAuBA;AACA;AACA;AACA;AACO,SAASA,aAAa,CAC3BC,IAAY,EACZC,UAAgC,GAAG,CAAC,CAAC,EACR;EAC7B,MAAMC,GAAG,GAAGD,UAAU,CAACE,SAAS,IAAI,IAAAC,uBAAc,EAACJ,IAAI,CAAC;EAExD,IAAI,CAACE,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EAEA,MAAMC,SAAS,GAAGE,eAAI,CAACC,IAAI,CAACN,IAAI,EAAEE,GAAG,CAAC;EAEtC,MAAMK,OAAO,GAAGC,UAAU,CAACL,SAAS,EAAEF,UAAU,CAACM,OAAO,CAAC;EAEzD,MAAME,YAAY,GAAGR,UAAU,CAACQ,YAAY,GACxCJ,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEF,UAAU,CAACQ,YAAY,CAAC,GAC7C,IAAAC,qBAAY,EAACL,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEI,OAAO,CAAC,CAAC;EAC/C,MAAMI,eAAe,GAAG,IAAAC,gCAAe,EAACT,SAAS,EAAE,KAAK,CAAC;EAEzD,IAAI,CAACM,YAAY,IAAI,CAACE,eAAe,EAAE;IACrC,OAAO,IAAI;EACb;EAEA,MAAME,WAAW,GACfZ,UAAU,CAACY,WAAW,IAAI,IAAAC,iCAAc,EAACL,YAAY,EAAEE,eAAe,CAAC;EAEzE,IAAI,CAACE,WAAW,EAAE;IAChB,MAAM,KAAIE,oBAAQ,EACf,qCAAoCN,YAAa,QAAOE,eAAgB,EAAC,CAC3E;EACH;EAEA,MAAMK,aAAa,GAAGL,eAAe,GACjCM,gBAAgB,CAACN,eAAe,EAAEE,WAAW,CAAC,GAC9CA,WAAW;EACf,MAAMK,YAAY,GAAG,IAAAC,wBAAe,EAACV,YAAY,IAAI,EAAE,CAAC,IAAI,EAAE;EAE9D,OAAO;IACLN,SAAS;IACTI,OAAO;IACPM,WAAW;IACXG,aAAa;IACbE,YAAY;IACZE,uBAAuB,EAAEnB,UAAU,CAACmB,uBAAuB;IAC3DC,sBAAsB,EAAEpB,UAAU,CAACoB,sBAAsB;IACzDC,MAAM,EAAErB,UAAU,CAACqB,MAAM,IAAI;EAC/B,CAAC;AACH;AAEA,SAASL,gBAAgB,CAACN,eAAuB,EAAEE,WAAmB,EAAE;EACtE,IAAIU,KAAK,GAAGV,WAAW;EAEvB,MAAMG,aAAa,GAAG,IAAAQ,wDAAqC,EAACb,eAAe,CAAC;EAC5E,IAAIK,aAAa,EAAE;IACjBO,KAAK,GAAGP,aAAa;EACvB;EACA,OAAOO,KAAK;AACd;AAEA,SAASf,UAAU,CAACL,SAAiB,EAAEsB,iBAAqC,EAAE;EAC5E,IAAIlB,OAAO,GAAG,EAAE;EAChB,IACE,OAAOkB,iBAAiB,KAAK,QAAQ,IACrCC,aAAE,CAACC,UAAU,CAACtB,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEsB,iBAAiB,CAAC,CAAC,EACtD;IACAlB,OAAO,GAAGkB,iBAAiB;EAC7B,CAAC,MAAM,IAAIC,aAAE,CAACC,UAAU,CAACtB,eAAI,CAACC,IAAI,CAACH,SAAS,EAAE,KAAK,CAAC,CAAC,EAAE;IACrDI,OAAO,GAAG,KAAK;EACjB;EACA,OAAOA,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACO,SAASqB,gBAAgB,CAC9B5B,IAAY,EACZC,UAA0C,GAAG,CAAC,CAAC,EACf;EAChC,IAAIA,UAAU,KAAK,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,MAAMC,GAAG,GAAGD,UAAU,CAACE,SAAS,IAAI,IAAAC,uBAAc,EAACJ,IAAI,CAAC;EAExD,IAAI,CAACE,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EAEA,MAAMC,SAAS,GAAGE,eAAI,CAACC,IAAI,CAACN,IAAI,EAAEE,GAAG,CAAC;EACtC,MAAMO,YAAY,GAAGR,UAAU,CAACQ,YAAY,GACxCJ,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEF,UAAU,CAACQ,YAAY,CAAC,GAC7C,IAAAC,qBAAY,EAACP,SAAS,CAAC;EAC3B,MAAMQ,eAAe,GAAG,IAAAC,gCAAe,EAACT,SAAS,EAAE,IAAI,CAAC;EACxD,MAAM0B,mBAAmB,GACvB5B,UAAU,CAAC6B,6BAA6B,IAAI,IAAI,IAChD7B,UAAU,CAAC8B,uBAAuB,IAAI,IAAI,IAC1C9B,UAAU,CAAC+B,mBAAmB,IAAI,IAAI,IACtC,CAACvB,YAAY,IACb,CAACE,eAAe;EAElB,IAAI,CAACF,YAAY,IAAI,CAACE,eAAe,IAAI,CAACkB,mBAAmB,EAAE;IAC7D,OAAO,IAAI;EACb;EAEA,IAAII,iBAAiB,GAAG,IAAI;IAC1BC,eAAe,GAAG,IAAI;EAExB,IAAI,CAACL,mBAAmB,EAAE;IACxB,MAAMhB,WAAW,GACfZ,UAAU,CAACY,WAAW,IAAI,IAAAC,iCAAc,EAACL,YAAY,EAAEE,eAAe,CAAC;IACzE,MAAMwB,gBAAgB,GAAG,IAAAC,6BAAoB,EAACjC,SAAS,CAAC;;IAExD;AACJ;AACA;IACI,IAAI,CAACgC,gBAAgB,EAAE;MACrB,OAAO,IAAI;IACb;IAEAF,iBAAiB,GACfhC,UAAU,CAACgC,iBAAiB,IAC3B,UAASpB,WAAY,IAAGsB,gBAAiB,GAAE;IAE9CD,eAAe,GAAGjC,UAAU,CAACiC,eAAe,IAAK,OAAMC,gBAAiB,IAAG;EAC7E;EAEA,MAAME,UAAU,GAAGpC,UAAU,CAACoC,UAAU,IAAI,EAAE;EAC9C,MAAMjB,uBAAuB,GAAGnB,UAAU,CAACmB,uBAAuB;EAClE,MAAMkB,WAAW,GACfrC,UAAU,CAACqC,WAAW,IAAI,IAAAC,gCAAe,EAACvC,IAAI,EAAEG,SAAS,CAAC;EAC5D,MAAMqC,oBAAoB,GACxBvC,UAAU,CAACuC,oBAAoB,IAAI,IAAAC,kDAAwB,EAACzC,IAAI,CAAC;EACnE,IAAI0C,cAAc,GAAGzC,UAAU,CAACyC,cAAc,GAC1CrC,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEF,UAAU,CAACyC,cAAc,CAAC,GAC/CrC,eAAI,CAACC,IAAI,CAACH,SAAS,EAAE,mDAAmD,CAAC;EAC7E,MAAM2B,6BAA6B,GACjC7B,UAAU,CAAC6B,6BAA6B,IAAI,IAAI;EAClD,MAAME,mBAAmB,GAAG/B,UAAU,CAAC+B,mBAAmB,IAAI,IAAI;EAClE,IAAID,uBAAuB,GAAG9B,UAAU,CAAC8B,uBAAuB,GAC5D1B,eAAI,CAACC,IAAI,CAACH,SAAS,EAAEF,UAAU,CAAC8B,uBAAuB,CAAC,GACxD,IAAI;EAER,IAAIY,OAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;IAChCF,cAAc,GAAGA,cAAc,CAACG,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACnD,IAAId,uBAAuB,EAAE;MAC3BA,uBAAuB,GAAGA,uBAAuB,CAACc,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACvE;EACF;EAEA,OAAO;IACL1C,SAAS;IACT8B,iBAAiB;IACjBC,eAAe;IACfG,UAAU;IACVjB,uBAAuB;IACvBkB,WAAW;IACXE,oBAAoB;IACpBE,cAAc;IACdZ,6BAA6B;IAC7BC,uBAAuB;IACvBC,mBAAmB;IACnBH;EACF,CAAC;AACH"}