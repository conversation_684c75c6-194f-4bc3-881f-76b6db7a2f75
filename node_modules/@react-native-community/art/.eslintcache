[{"/Users/<USER>/projects/oss/art/babel.config.js": "1", "/Users/<USER>/projects/oss/art/example/App.js": "2", "/Users/<USER>/projects/oss/art/example/components/Heart.js": "3", "/Users/<USER>/projects/oss/art/example/index.js": "4", "/Users/<USER>/projects/oss/art/example/metro.config.js": "5", "/Users/<USER>/projects/oss/art/lib/ARTSerializablePath.js": "6", "/Users/<USER>/projects/oss/art/lib/ClippingRectangle.js": "7", "/Users/<USER>/projects/oss/art/lib/Group.js": "8", "/Users/<USER>/projects/oss/art/lib/helpers.js": "9", "/Users/<USER>/projects/oss/art/lib/LinearGradient.js": "10", "/Users/<USER>/projects/oss/art/lib/nativeComponents.js": "11", "/Users/<USER>/projects/oss/art/lib/Pattern.js": "12", "/Users/<USER>/projects/oss/art/lib/RadialGradient.js": "13", "/Users/<USER>/projects/oss/art/lib/Shape.js": "14", "/Users/<USER>/projects/oss/art/lib/Surface.js": "15", "/Users/<USER>/projects/oss/art/lib/Text.js": "16", "/Users/<USER>/projects/oss/art/lib/types.js": "17", "/Users/<USER>/projects/oss/art/example/components/CustomShape.js": "18", "/Users/<USER>/projects/oss/art/example/components/CustomText.js": "19", "/Users/<USER>/projects/oss/art/lib/__tests__/test_helpers.js": "20", "/Users/<USER>/projects/oss/art/lib/index.js": "21"}, {"size": 77, "mtime": 1559925338202, "results": "22", "hashOfConfig": "23"}, {"size": 573, "mtime": 1560036705786, "results": "24", "hashOfConfig": "23"}, {"size": 927, "mtime": 1560856715032, "results": "25", "hashOfConfig": "23"}, {"size": 183, "mtime": 1559925338211, "results": "26", "hashOfConfig": "23"}, {"size": 300, "mtime": 1560001335643, "results": "27", "hashOfConfig": "23"}, {"size": 1841, "mtime": 1560974617331, "results": "28", "hashOfConfig": "23"}, {"size": 1259, "mtime": 1560974617332, "results": "29", "hashOfConfig": "23"}, {"size": 1016, "mtime": 1560036705790, "results": "30", "hashOfConfig": "23"}, {"size": 8232, "mtime": 1560974662864, "results": "31", "hashOfConfig": "23"}, {"size": 1000, "mtime": 1560036705790, "results": "32", "hashOfConfig": "23"}, {"size": 557, "mtime": 1560974617332, "results": "33", "hashOfConfig": "23"}, {"size": 472, "mtime": 1560856715033, "results": "34", "hashOfConfig": "23"}, {"size": 1299, "mtime": 1560036705791, "results": "35", "hashOfConfig": "23"}, {"size": 1706, "mtime": 1560856715033, "results": "36", "hashOfConfig": "23"}, {"size": 889, "mtime": 1560036705792, "results": "37", "hashOfConfig": "23"}, {"size": 1983, "mtime": 1560856715034, "results": "38", "hashOfConfig": "23"}, {"size": 1149, "mtime": 1560974617332, "results": "39", "hashOfConfig": "23"}, {"size": 995, "mtime": 1560856715031, "results": "40", "hashOfConfig": "23"}, {"size": 700, "mtime": 1560856715031, "results": "41", "hashOfConfig": "23"}, {"size": 2053, "mtime": 1560975086046, "results": "42", "hashOfConfig": "43"}, {"size": 716, "mtime": 1560036705793, "results": "44", "hashOfConfig": "23"}, {"filePath": "45", "messages": "46", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6qbzhb", {"filePath": "47", "messages": "48", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "errorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "107rb5c", {"filePath": "85", "messages": "86", "errorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/projects/oss/art/babel.config.js", [], "/Users/<USER>/projects/oss/art/example/App.js", [], "/Users/<USER>/projects/oss/art/example/components/Heart.js", [], "/Users/<USER>/projects/oss/art/example/index.js", [], "/Users/<USER>/projects/oss/art/example/metro.config.js", [], "/Users/<USER>/projects/oss/art/lib/ARTSerializablePath.js", [], "/Users/<USER>/projects/oss/art/lib/ClippingRectangle.js", [], "/Users/<USER>/projects/oss/art/lib/Group.js", [], "/Users/<USER>/projects/oss/art/lib/helpers.js", [], "/Users/<USER>/projects/oss/art/lib/LinearGradient.js", [], "/Users/<USER>/projects/oss/art/lib/nativeComponents.js", [], "/Users/<USER>/projects/oss/art/lib/Pattern.js", [], "/Users/<USER>/projects/oss/art/lib/RadialGradient.js", [], "/Users/<USER>/projects/oss/art/lib/Shape.js", [], "/Users/<USER>/projects/oss/art/lib/Surface.js", [], "/Users/<USER>/projects/oss/art/lib/Text.js", [], "/Users/<USER>/projects/oss/art/lib/types.js", [], "/Users/<USER>/projects/oss/art/example/components/CustomShape.js", [], "/Users/<USER>/projects/oss/art/example/components/CustomText.js", [], "/Users/<USER>/projects/oss/art/lib/__tests__/test_helpers.js", ["87"], "/Users/<USER>/projects/oss/art/lib/index.js", [], {"ruleId": "88", "severity": 1, "message": "89", "line": 18, "column": 40, "nodeType": "90", "messageId": "91", "endLine": 18, "endColumn": 47}, "no-control-regex", "Unexpected control character(s) in regular expression: \\x0d, \\x0a.", "Literal", "unexpected"]