{"version": 3, "names": ["getDependencyConfig", "root", "dependencyName", "finalConfig", "config", "userConfig", "merge", "name", "platforms", "Object", "keys", "reduce", "dependency", "platform", "platformConfig", "length", "dependencyConfig", "dependencies", "getReactNativeVersion", "reactNativePath", "semver", "version", "current", "major", "minor", "e", "UnknownProjectError", "removeDuplicateCommands", "commands", "uniqueCommandsMap", "Map", "for<PERSON>ach", "command", "set", "Array", "from", "values", "loadConfig", "projectRoot", "findProjectRoot", "selectedPlatform", "lazyProject", "readConfigFromDisk", "initialConfig", "path", "resolve", "resolveReactNativePath", "reactNativeVersion", "healthChecks", "assets", "project", "projectConfig", "Set", "findDependencies", "acc", "localDependencyRoot", "resolveNodeModuleDir", "readDependencyConfigFromDisk", "assign", "loadConfigAsync", "readConfigFromDiskAsync", "accPromise", "readDependencyConfigFromDiskAsync", "Promise"], "sources": ["../src/loadConfig.ts"], "sourcesContent": ["import path from 'path';\nimport {\n  UserDependencyConfig,\n  ProjectConfig,\n  DependencyConfig,\n  UserConfig,\n  Config,\n  Command,\n} from '@react-native-community/cli-types';\nimport {\n  findProjectRoot,\n  version,\n  resolveNodeModuleDir,\n  UnknownProjectError,\n} from '@react-native-community/cli-tools';\nimport findDependencies from './findDependencies';\nimport resolveReactNativePath from './resolveReactNativePath';\nimport {\n  readConfigFromDisk,\n  readConfigFromDiskAsync,\n  readDependencyConfigFromDisk,\n  readDependencyConfigFromDiskAsync,\n} from './readConfigFromDisk';\nimport assign from './assign';\nimport merge from './merge';\n\nfunction getDependencyConfig(\n  root: string,\n  dependencyName: string,\n  finalConfig: Config,\n  config: UserDependencyConfig,\n  userConfig: UserConfig,\n): DependencyConfig {\n  return merge(\n    {\n      root,\n      name: dependencyName,\n      platforms: Object.keys(finalConfig.platforms).reduce(\n        (dependency, platform) => {\n          const platformConfig = finalConfig.platforms[platform];\n          dependency[platform] =\n            // Linking platforms is not supported\n            Object.keys(config.platforms).length > 0 || !platformConfig\n              ? null\n              : platformConfig.dependencyConfig(\n                  root,\n                  config.dependency.platforms?.[platform],\n                );\n          return dependency;\n        },\n        {} as Config['platforms'],\n      ),\n    },\n    userConfig.dependencies[dependencyName] || {},\n  ) as DependencyConfig;\n}\n\n// Try our best to figure out what version of React Native we're running. This is\n// currently being used to get our deeplinks working, so it's only worried with\n// the major and minor version.\nfunction getReactNativeVersion(reactNativePath: string) {\n  try {\n    let semver = version.current(reactNativePath);\n    if (semver) {\n      // Retain only these version, since they correspond with our documentation.\n      return `${semver.major}.${semver.minor}`;\n    }\n  } catch (e) {\n    // If we don't seem to be in a well formed project, give up quietly.\n    if (!(e instanceof UnknownProjectError)) {\n      throw e;\n    }\n  }\n  return 'unknown';\n}\n\nconst removeDuplicateCommands = <T extends boolean>(commands: Command<T>[]) => {\n  const uniqueCommandsMap = new Map();\n\n  commands.forEach((command) => {\n    uniqueCommandsMap.set(command.name, command);\n  });\n\n  return Array.from(uniqueCommandsMap.values());\n};\n\n/**\n * Loads CLI configuration\n */\nexport default function loadConfig({\n  projectRoot = findProjectRoot(),\n  selectedPlatform,\n}: {\n  projectRoot?: string;\n  selectedPlatform?: string;\n}): Config {\n  let lazyProject: ProjectConfig;\n  const userConfig = readConfigFromDisk(projectRoot);\n\n  const initialConfig: Config = {\n    root: projectRoot,\n    get reactNativePath() {\n      return userConfig.reactNativePath\n        ? path.resolve(projectRoot, userConfig.reactNativePath)\n        : resolveReactNativePath(projectRoot);\n    },\n    get reactNativeVersion() {\n      return getReactNativeVersion(initialConfig.reactNativePath);\n    },\n    dependencies: userConfig.dependencies,\n    commands: userConfig.commands,\n    healthChecks: userConfig.healthChecks || [],\n    platforms: userConfig.platforms,\n    assets: userConfig.assets,\n    get project() {\n      if (lazyProject) {\n        return lazyProject;\n      }\n\n      lazyProject = {};\n      for (const platform in finalConfig.platforms) {\n        const platformConfig = finalConfig.platforms[platform];\n        if (platformConfig) {\n          lazyProject[platform] = platformConfig.projectConfig(\n            projectRoot,\n            userConfig.project[platform] || {},\n          );\n        }\n      }\n\n      return lazyProject;\n    },\n  };\n\n  const finalConfig = Array.from(\n    new Set([\n      ...Object.keys(userConfig.dependencies),\n      ...findDependencies(projectRoot),\n    ]),\n  ).reduce((acc: Config, dependencyName) => {\n    const localDependencyRoot =\n      userConfig.dependencies[dependencyName] &&\n      userConfig.dependencies[dependencyName].root;\n    try {\n      let root =\n        localDependencyRoot ||\n        resolveNodeModuleDir(projectRoot, dependencyName);\n      let config = readDependencyConfigFromDisk(root, dependencyName);\n\n      return assign({}, acc, {\n        dependencies: assign({}, acc.dependencies, {\n          get [dependencyName](): DependencyConfig {\n            return getDependencyConfig(\n              root,\n              dependencyName,\n              finalConfig,\n              config,\n              userConfig,\n            );\n          },\n        }),\n        commands: removeDuplicateCommands([\n          ...config.commands,\n          ...acc.commands,\n        ]),\n        platforms: {\n          ...acc.platforms,\n          ...(selectedPlatform && config.platforms[selectedPlatform]\n            ? {[selectedPlatform]: config.platforms[selectedPlatform]}\n            : config.platforms),\n        },\n        healthChecks: [...acc.healthChecks, ...config.healthChecks],\n      }) as Config;\n    } catch {\n      return acc;\n    }\n  }, initialConfig);\n\n  return finalConfig;\n}\n\n/**\n * Load CLI configuration asynchronously, which supports reading ESM modules.\n */\n\nexport async function loadConfigAsync({\n  projectRoot = findProjectRoot(),\n  selectedPlatform,\n}: {\n  projectRoot?: string;\n  selectedPlatform?: string;\n}): Promise<Config> {\n  let lazyProject: ProjectConfig;\n  const userConfig = await readConfigFromDiskAsync(projectRoot);\n\n  const initialConfig: Config = {\n    root: projectRoot,\n    get reactNativePath() {\n      return userConfig.reactNativePath\n        ? path.resolve(projectRoot, userConfig.reactNativePath)\n        : resolveReactNativePath(projectRoot);\n    },\n    get reactNativeVersion() {\n      return getReactNativeVersion(initialConfig.reactNativePath);\n    },\n    dependencies: userConfig.dependencies,\n    commands: userConfig.commands,\n    healthChecks: userConfig.healthChecks || [],\n    platforms: userConfig.platforms,\n    assets: userConfig.assets,\n    get project() {\n      if (lazyProject) {\n        return lazyProject;\n      }\n\n      lazyProject = {};\n      for (const platform in finalConfig.platforms) {\n        const platformConfig = finalConfig.platforms[platform];\n        if (platformConfig) {\n          lazyProject[platform] = platformConfig.projectConfig(\n            projectRoot,\n            userConfig.project[platform] || {},\n          );\n        }\n      }\n\n      return lazyProject;\n    },\n  };\n\n  const finalConfig = await Array.from(\n    new Set([\n      ...Object.keys(userConfig.dependencies),\n      ...findDependencies(projectRoot),\n    ]),\n  ).reduce(async (accPromise: Promise<Config>, dependencyName) => {\n    const acc = await accPromise;\n    const localDependencyRoot =\n      userConfig.dependencies[dependencyName] &&\n      userConfig.dependencies[dependencyName].root;\n    try {\n      let root =\n        localDependencyRoot ||\n        resolveNodeModuleDir(projectRoot, dependencyName);\n      let config = await readDependencyConfigFromDiskAsync(\n        root,\n        dependencyName,\n      );\n\n      return assign({}, acc, {\n        dependencies: assign({}, acc.dependencies, {\n          get [dependencyName](): DependencyConfig {\n            return getDependencyConfig(\n              root,\n              dependencyName,\n              finalConfig,\n              config,\n              userConfig,\n            );\n          },\n        }),\n        commands: removeDuplicateCommands([\n          ...config.commands,\n          ...acc.commands,\n        ]),\n        platforms: {\n          ...acc.platforms,\n          ...(selectedPlatform && config.platforms[selectedPlatform]\n            ? {[selectedPlatform]: config.platforms[selectedPlatform]}\n            : config.platforms),\n        },\n        healthChecks: [...acc.healthChecks, ...config.healthChecks],\n      }) as Config;\n    } catch {\n      return acc;\n    }\n  }, Promise.resolve(initialConfig));\n\n  return finalConfig;\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AASA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMA;AACA;AACA;AAMA;AACA;AAA4B;AAE5B,SAASA,mBAAmB,CAC1BC,IAAY,EACZC,cAAsB,EACtBC,WAAmB,EACnBC,MAA4B,EAC5BC,UAAsB,EACJ;EAClB,OAAO,IAAAC,cAAK,EACV;IACEL,IAAI;IACJM,IAAI,EAAEL,cAAc;IACpBM,SAAS,EAAEC,MAAM,CAACC,IAAI,CAACP,WAAW,CAACK,SAAS,CAAC,CAACG,MAAM,CAClD,CAACC,UAAU,EAAEC,QAAQ,KAAK;MAAA;MACxB,MAAMC,cAAc,GAAGX,WAAW,CAACK,SAAS,CAACK,QAAQ,CAAC;MACtDD,UAAU,CAACC,QAAQ,CAAC;MAClB;MACAJ,MAAM,CAACC,IAAI,CAACN,MAAM,CAACI,SAAS,CAAC,CAACO,MAAM,GAAG,CAAC,IAAI,CAACD,cAAc,GACvD,IAAI,GACJA,cAAc,CAACE,gBAAgB,CAC7Bf,IAAI,2BACJG,MAAM,CAACQ,UAAU,CAACJ,SAAS,0DAA3B,sBAA8BK,QAAQ,CAAC,CACxC;MACP,OAAOD,UAAU;IACnB,CAAC,EACD,CAAC,CAAC;EAEN,CAAC,EACDP,UAAU,CAACY,YAAY,CAACf,cAAc,CAAC,IAAI,CAAC,CAAC,CAC9C;AACH;;AAEA;AACA;AACA;AACA,SAASgB,qBAAqB,CAACC,eAAuB,EAAE;EACtD,IAAI;IACF,IAAIC,MAAM,GAAGC,mBAAO,CAACC,OAAO,CAACH,eAAe,CAAC;IAC7C,IAAIC,MAAM,EAAE;MACV;MACA,OAAQ,GAAEA,MAAM,CAACG,KAAM,IAAGH,MAAM,CAACI,KAAM,EAAC;IAC1C;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV;IACA,IAAI,EAAEA,CAAC,YAAYC,+BAAmB,CAAC,EAAE;MACvC,MAAMD,CAAC;IACT;EACF;EACA,OAAO,SAAS;AAClB;AAEA,MAAME,uBAAuB,GAAuBC,QAAsB,IAAK;EAC7E,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,EAAE;EAEnCF,QAAQ,CAACG,OAAO,CAAEC,OAAO,IAAK;IAC5BH,iBAAiB,CAACI,GAAG,CAACD,OAAO,CAACzB,IAAI,EAAEyB,OAAO,CAAC;EAC9C,CAAC,CAAC;EAEF,OAAOE,KAAK,CAACC,IAAI,CAACN,iBAAiB,CAACO,MAAM,EAAE,CAAC;AAC/C,CAAC;;AAED;AACA;AACA;AACe,SAASC,UAAU,CAAC;EACjCC,WAAW,GAAG,IAAAC,2BAAe,GAAE;EAC/BC;AAIF,CAAC,EAAU;EACT,IAAIC,WAA0B;EAC9B,MAAMpC,UAAU,GAAG,IAAAqC,sCAAkB,EAACJ,WAAW,CAAC;EAElD,MAAMK,aAAqB,GAAG;IAC5B1C,IAAI,EAAEqC,WAAW;IACjB,IAAInB,eAAe,GAAG;MACpB,OAAOd,UAAU,CAACc,eAAe,GAC7ByB,eAAI,CAACC,OAAO,CAACP,WAAW,EAAEjC,UAAU,CAACc,eAAe,CAAC,GACrD,IAAA2B,+BAAsB,EAACR,WAAW,CAAC;IACzC,CAAC;IACD,IAAIS,kBAAkB,GAAG;MACvB,OAAO7B,qBAAqB,CAACyB,aAAa,CAACxB,eAAe,CAAC;IAC7D,CAAC;IACDF,YAAY,EAAEZ,UAAU,CAACY,YAAY;IACrCW,QAAQ,EAAEvB,UAAU,CAACuB,QAAQ;IAC7BoB,YAAY,EAAE3C,UAAU,CAAC2C,YAAY,IAAI,EAAE;IAC3CxC,SAAS,EAAEH,UAAU,CAACG,SAAS;IAC/ByC,MAAM,EAAE5C,UAAU,CAAC4C,MAAM;IACzB,IAAIC,OAAO,GAAG;MACZ,IAAIT,WAAW,EAAE;QACf,OAAOA,WAAW;MACpB;MAEAA,WAAW,GAAG,CAAC,CAAC;MAChB,KAAK,MAAM5B,QAAQ,IAAIV,WAAW,CAACK,SAAS,EAAE;QAC5C,MAAMM,cAAc,GAAGX,WAAW,CAACK,SAAS,CAACK,QAAQ,CAAC;QACtD,IAAIC,cAAc,EAAE;UAClB2B,WAAW,CAAC5B,QAAQ,CAAC,GAAGC,cAAc,CAACqC,aAAa,CAClDb,WAAW,EACXjC,UAAU,CAAC6C,OAAO,CAACrC,QAAQ,CAAC,IAAI,CAAC,CAAC,CACnC;QACH;MACF;MAEA,OAAO4B,WAAW;IACpB;EACF,CAAC;EAED,MAAMtC,WAAW,GAAG+B,KAAK,CAACC,IAAI,CAC5B,IAAIiB,GAAG,CAAC,CACN,GAAG3C,MAAM,CAACC,IAAI,CAACL,UAAU,CAACY,YAAY,CAAC,EACvC,GAAG,IAAAoC,yBAAgB,EAACf,WAAW,CAAC,CACjC,CAAC,CACH,CAAC3B,MAAM,CAAC,CAAC2C,GAAW,EAAEpD,cAAc,KAAK;IACxC,MAAMqD,mBAAmB,GACvBlD,UAAU,CAACY,YAAY,CAACf,cAAc,CAAC,IACvCG,UAAU,CAACY,YAAY,CAACf,cAAc,CAAC,CAACD,IAAI;IAC9C,IAAI;MACF,IAAIA,IAAI,GACNsD,mBAAmB,IACnB,IAAAC,gCAAoB,EAAClB,WAAW,EAAEpC,cAAc,CAAC;MACnD,IAAIE,MAAM,GAAG,IAAAqD,gDAA4B,EAACxD,IAAI,EAAEC,cAAc,CAAC;MAE/D,OAAO,IAAAwD,eAAM,EAAC,CAAC,CAAC,EAAEJ,GAAG,EAAE;QACrBrC,YAAY,EAAE,IAAAyC,eAAM,EAAC,CAAC,CAAC,EAAEJ,GAAG,CAACrC,YAAY,EAAE;UACzC,KAAKf,cAAc,IAAsB;YACvC,OAAOF,mBAAmB,CACxBC,IAAI,EACJC,cAAc,EACdC,WAAW,EACXC,MAAM,EACNC,UAAU,CACX;UACH;QACF,CAAC,CAAC;QACFuB,QAAQ,EAAED,uBAAuB,CAAC,CAChC,GAAGvB,MAAM,CAACwB,QAAQ,EAClB,GAAG0B,GAAG,CAAC1B,QAAQ,CAChB,CAAC;QACFpB,SAAS,EAAE;UACT,GAAG8C,GAAG,CAAC9C,SAAS;UAChB,IAAIgC,gBAAgB,IAAIpC,MAAM,CAACI,SAAS,CAACgC,gBAAgB,CAAC,GACtD;YAAC,CAACA,gBAAgB,GAAGpC,MAAM,CAACI,SAAS,CAACgC,gBAAgB;UAAC,CAAC,GACxDpC,MAAM,CAACI,SAAS;QACtB,CAAC;QACDwC,YAAY,EAAE,CAAC,GAAGM,GAAG,CAACN,YAAY,EAAE,GAAG5C,MAAM,CAAC4C,YAAY;MAC5D,CAAC,CAAC;IACJ,CAAC,CAAC,MAAM;MACN,OAAOM,GAAG;IACZ;EACF,CAAC,EAAEX,aAAa,CAAC;EAEjB,OAAOxC,WAAW;AACpB;;AAEA;AACA;AACA;;AAEO,eAAewD,eAAe,CAAC;EACpCrB,WAAW,GAAG,IAAAC,2BAAe,GAAE;EAC/BC;AAIF,CAAC,EAAmB;EAClB,IAAIC,WAA0B;EAC9B,MAAMpC,UAAU,GAAG,MAAM,IAAAuD,2CAAuB,EAACtB,WAAW,CAAC;EAE7D,MAAMK,aAAqB,GAAG;IAC5B1C,IAAI,EAAEqC,WAAW;IACjB,IAAInB,eAAe,GAAG;MACpB,OAAOd,UAAU,CAACc,eAAe,GAC7ByB,eAAI,CAACC,OAAO,CAACP,WAAW,EAAEjC,UAAU,CAACc,eAAe,CAAC,GACrD,IAAA2B,+BAAsB,EAACR,WAAW,CAAC;IACzC,CAAC;IACD,IAAIS,kBAAkB,GAAG;MACvB,OAAO7B,qBAAqB,CAACyB,aAAa,CAACxB,eAAe,CAAC;IAC7D,CAAC;IACDF,YAAY,EAAEZ,UAAU,CAACY,YAAY;IACrCW,QAAQ,EAAEvB,UAAU,CAACuB,QAAQ;IAC7BoB,YAAY,EAAE3C,UAAU,CAAC2C,YAAY,IAAI,EAAE;IAC3CxC,SAAS,EAAEH,UAAU,CAACG,SAAS;IAC/ByC,MAAM,EAAE5C,UAAU,CAAC4C,MAAM;IACzB,IAAIC,OAAO,GAAG;MACZ,IAAIT,WAAW,EAAE;QACf,OAAOA,WAAW;MACpB;MAEAA,WAAW,GAAG,CAAC,CAAC;MAChB,KAAK,MAAM5B,QAAQ,IAAIV,WAAW,CAACK,SAAS,EAAE;QAC5C,MAAMM,cAAc,GAAGX,WAAW,CAACK,SAAS,CAACK,QAAQ,CAAC;QACtD,IAAIC,cAAc,EAAE;UAClB2B,WAAW,CAAC5B,QAAQ,CAAC,GAAGC,cAAc,CAACqC,aAAa,CAClDb,WAAW,EACXjC,UAAU,CAAC6C,OAAO,CAACrC,QAAQ,CAAC,IAAI,CAAC,CAAC,CACnC;QACH;MACF;MAEA,OAAO4B,WAAW;IACpB;EACF,CAAC;EAED,MAAMtC,WAAW,GAAG,MAAM+B,KAAK,CAACC,IAAI,CAClC,IAAIiB,GAAG,CAAC,CACN,GAAG3C,MAAM,CAACC,IAAI,CAACL,UAAU,CAACY,YAAY,CAAC,EACvC,GAAG,IAAAoC,yBAAgB,EAACf,WAAW,CAAC,CACjC,CAAC,CACH,CAAC3B,MAAM,CAAC,OAAOkD,UAA2B,EAAE3D,cAAc,KAAK;IAC9D,MAAMoD,GAAG,GAAG,MAAMO,UAAU;IAC5B,MAAMN,mBAAmB,GACvBlD,UAAU,CAACY,YAAY,CAACf,cAAc,CAAC,IACvCG,UAAU,CAACY,YAAY,CAACf,cAAc,CAAC,CAACD,IAAI;IAC9C,IAAI;MACF,IAAIA,IAAI,GACNsD,mBAAmB,IACnB,IAAAC,gCAAoB,EAAClB,WAAW,EAAEpC,cAAc,CAAC;MACnD,IAAIE,MAAM,GAAG,MAAM,IAAA0D,qDAAiC,EAClD7D,IAAI,EACJC,cAAc,CACf;MAED,OAAO,IAAAwD,eAAM,EAAC,CAAC,CAAC,EAAEJ,GAAG,EAAE;QACrBrC,YAAY,EAAE,IAAAyC,eAAM,EAAC,CAAC,CAAC,EAAEJ,GAAG,CAACrC,YAAY,EAAE;UACzC,KAAKf,cAAc,IAAsB;YACvC,OAAOF,mBAAmB,CACxBC,IAAI,EACJC,cAAc,EACdC,WAAW,EACXC,MAAM,EACNC,UAAU,CACX;UACH;QACF,CAAC,CAAC;QACFuB,QAAQ,EAAED,uBAAuB,CAAC,CAChC,GAAGvB,MAAM,CAACwB,QAAQ,EAClB,GAAG0B,GAAG,CAAC1B,QAAQ,CAChB,CAAC;QACFpB,SAAS,EAAE;UACT,GAAG8C,GAAG,CAAC9C,SAAS;UAChB,IAAIgC,gBAAgB,IAAIpC,MAAM,CAACI,SAAS,CAACgC,gBAAgB,CAAC,GACtD;YAAC,CAACA,gBAAgB,GAAGpC,MAAM,CAACI,SAAS,CAACgC,gBAAgB;UAAC,CAAC,GACxDpC,MAAM,CAACI,SAAS;QACtB,CAAC;QACDwC,YAAY,EAAE,CAAC,GAAGM,GAAG,CAACN,YAAY,EAAE,GAAG5C,MAAM,CAAC4C,YAAY;MAC5D,CAAC,CAAC;IACJ,CAAC,CAAC,MAAM;MACN,OAAOM,GAAG;IACZ;EACF,CAAC,EAAES,OAAO,CAAClB,OAAO,CAACF,aAAa,CAAC,CAAC;EAElC,OAAOxC,WAAW;AACpB"}