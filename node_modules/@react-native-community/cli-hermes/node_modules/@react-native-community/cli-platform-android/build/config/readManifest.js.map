{"version": 3, "sources": ["../../src/config/readManifest.ts"], "names": ["readManifest", "manifestPath", "xml", "XmlDocument", "fs", "readFileSync", "error", "CLIError"], "mappings": ";;;;;;;AAOA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,YAAT,CAAsBC,YAAtB,EAA4C;AACzD,MAAI;AACF,WAAO,KAAIC,kBAAIC,WAAR,EAAoBC,cAAGC,YAAH,CAAgBJ,YAAhB,EAA8B,MAA9B,CAApB,CAAP;AACD,GAFD,CAEE,OAAOK,KAAP,EAAc;AACd,UAAM,KAAIC,oBAAJ,EACH,4CAA2CN,YAAa,EADrD,EAEJK,KAFI,CAAN;AAID;AACF", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport fs from 'fs';\nimport xml from 'xmldoc';\nimport {CLIError} from '@react-native-community/cli-tools';\n\nexport default function readManifest(manifestPath: string) {\n  try {\n    return new xml.XmlDocument(fs.readFileSync(manifestPath, 'utf8'));\n  } catch (error) {\n    throw new CLIError(\n      `Failed to parse Android Manifest file at ${manifestPath}`,\n      error,\n    );\n  }\n}\n"]}