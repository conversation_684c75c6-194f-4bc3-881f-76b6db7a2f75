{"version": 3, "sources": ["../../src/config/findPackageClassName.ts"], "names": ["getPackageClassName", "folder", "files", "glob", "sync", "cwd", "packages", "map", "filePath", "fs", "readFileSync", "path", "join", "matchClassName", "filter", "match", "length", "file", "nativeModuleMatch"], "mappings": ";;;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;AAMe,SAASA,mBAAT,CAA6BC,MAA7B,EAA6C;AAC1D,QAAMC,KAAK,GAAGC,gBAAKC,IAAL,CAAU,mBAAV,EAA+B;AAACC,IAAAA,GAAG,EAAEJ;AAAN,GAA/B,CAAd;;AAEA,QAAMK,QAAQ,GAAGJ,KAAK,CACnBK,GADc,CACTC,QAAD,IAAcC,cAAGC,YAAH,CAAgBC,gBAAKC,IAAL,CAAUX,MAAV,EAAkBO,QAAlB,CAAhB,EAA6C,MAA7C,CADJ,EAEdD,GAFc,CAEVM,cAFU,EAGdC,MAHc,CAGNC,KAAD,IAAWA,KAHJ,CAAjB,CAH0D,CAQ1D;;AACA,SAAOT,QAAQ,CAACU,MAAT,GAAkBV,QAAQ,CAAC,CAAD,CAAR,CAAY,CAAZ,CAAlB,GAAmC,IAA1C;AACD;;AAEM,SAASO,cAAT,CAAwBI,IAAxB,EAAsC;AAC3C,QAAMC,iBAAiB,GAAGD,IAAI,CAACF,KAAL,CACxB,gFADwB,CAA1B,CAD2C,CAI3C;AACA;;AACA,MAAIG,iBAAJ,EAAuB;AACrB,WAAOA,iBAAP;AACD,GAFD,MAEO;AACL,WAAOD,IAAI,CAACF,KAAL,CACL,kFADK,CAAP;AAGD;AACF", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport glob from 'glob';\nimport path from 'path';\n\nexport default function getPackageClassName(folder: string) {\n  const files = glob.sync('**/+(*.java|*.kt)', {cwd: folder});\n\n  const packages = files\n    .map((filePath) => fs.readFileSync(path.join(folder, filePath), 'utf8'))\n    .map(matchClassName)\n    .filter((match) => match);\n\n  // @ts-ignore\n  return packages.length ? packages[0][1] : null;\n}\n\nexport function matchClassName(file: string) {\n  const nativeModuleMatch = file.match(\n    /class\\s+(\\w+[^(\\s]*)[\\s\\w():]*(\\s+implements\\s+|:)[\\s\\w():,]*[^{]*ReactPackage/,\n  );\n  // We first check for implementation of ReactPackage to find native\n  // modules and then for subclasses of TurboReactPackage to find turbo modules.\n  if (nativeModuleMatch) {\n    return nativeModuleMatch;\n  } else {\n    return file.match(\n      /class\\s+(\\w+[^(\\s]*)[\\s\\w():]*(\\s+extends\\s+|:)[\\s\\w():,]*[^{]*TurboReactPackage/,\n    );\n  }\n}\n"]}