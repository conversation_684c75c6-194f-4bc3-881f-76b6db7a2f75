export declare const flat: {
    android: {
        src: {
            'AndroidManifest.xml': any;
            main: {
                com: {
                    some: {
                        example: {
                            [x: string]: any;
                            'Main.java': any;
                        };
                    };
                };
            };
        };
    };
};
export declare const nested: {
    android: {
        app: {
            src: {
                'AndroidManifest.xml': any;
                main: {
                    com: {
                        some: {
                            example: {
                                [x: string]: any;
                                'Main.java': any;
                            };
                        };
                    };
                };
            };
        };
    };
};
export declare const withExamples: {
    Examples: {
        android: {
            src: {
                'AndroidManifest.xml': any;
                main: {
                    com: {
                        some: {
                            example: {
                                [x: string]: any;
                                'Main.java': any;
                            };
                        };
                    };
                };
            };
        };
    };
    android: {
        src: {
            'AndroidManifest.xml': any;
            main: {
                com: {
                    some: {
                        example: {
                            [x: string]: any;
                            'Main.java': any;
                        };
                    };
                };
            };
        };
    };
};
//# sourceMappingURL=projects.d.ts.map