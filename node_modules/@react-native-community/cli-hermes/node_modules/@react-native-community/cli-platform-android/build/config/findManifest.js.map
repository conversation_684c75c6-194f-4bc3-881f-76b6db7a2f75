{"version": 3, "sources": ["../../src/config/findManifest.ts"], "names": ["findManifest", "folder", "manifestPath", "glob", "sync", "path", "join", "cwd", "ignore"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,YAAT,CAAsBC,MAAtB,EAAsC;AACnD,QAAMC,YAAY,GAAGC,gBAAKC,IAAL,CAAUC,gBAAKC,IAAL,CAAU,IAAV,EAAgB,qBAAhB,CAAV,EAAkD;AACrEC,IAAAA,GAAG,EAAEN,MADgE;AAErEO,IAAAA,MAAM,EAAE,CACN,iBADM,EAEN,aAFM,EAGN,aAHM,EAIN,aAJM,EAKN,aALM;AAF6D,GAAlD,EASlB,CATkB,CAArB;;AAWA,SAAON,YAAY,GAAGG,gBAAKC,IAAL,CAAUL,MAAV,EAAkBC,YAAlB,CAAH,GAAqC,IAAxD;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport glob from 'glob';\nimport path from 'path';\n\nexport default function findManifest(folder: string) {\n  const manifestPath = glob.sync(path.join('**', 'AndroidManifest.xml'), {\n    cwd: folder,\n    ignore: [\n      'node_modules/**',\n      '**/build/**',\n      '**/debug/**',\n      'Examples/**',\n      'examples/**',\n    ],\n  })[0];\n\n  return manifestPath ? path.join(folder, manifestPath) : null;\n}\n"]}