{"version": 3, "sources": ["../../src/config/index.ts"], "names": ["getPackageName", "manifest", "attr", "package", "projectConfig", "root", "userConfig", "src", "sourceDir", "path", "join", "appName", "getAppName", "is<PERSON><PERSON>", "indexOf", "manifestPath", "packageName", "Error", "packageFolder", "replace", "sep", "mainFilePath", "stringsPath", "<PERSON>GradlePath", "assetsPath", "buildGradlePath", "dependencyConfiguration", "folder", "userConfigAppName", "fs", "existsSync", "dependencyConfig", "packageClassName", "packageImportPath", "packageInstance", "buildTypes"], "mappings": ";;;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;;AACA;;AACA;;;;AAbA;AACA;AACA;AACA;AACA;AACA;AACA;AAeA,MAAMA,cAAc,GAAIC,QAAD,IAA2BA,QAAQ,CAACC,IAAT,CAAcC,OAAhE;AAEA;AACA;AACA;AACA;;;AACO,SAASC,aAAT,CACLC,IADK,EAELC,UAAgC,GAAG,EAF9B,EAGwB;AAC7B,QAAMC,GAAG,GAAGD,UAAU,CAACE,SAAX,IAAwB,6BAAeH,IAAf,CAApC;;AAEA,MAAI,CAACE,GAAL,EAAU;AACR,WAAO,IAAP;AACD;;AAED,QAAMC,SAAS,GAAGC,gBAAKC,IAAL,CAAUL,IAAV,EAAgBE,GAAhB,CAAlB;;AACA,QAAMI,OAAO,GAAGC,UAAU,CAACJ,SAAD,EAAYF,UAAU,CAACK,OAAvB,CAA1B;AACA,QAAME,MAAM,GAAGL,SAAS,CAACM,OAAV,CAAkB,KAAlB,MAA6B,CAAC,CAA7C;AACA,QAAMC,YAAY,GAAGT,UAAU,CAACS,YAAX,GACjBN,gBAAKC,IAAL,CAAUF,SAAV,EAAqBF,UAAU,CAACS,YAAhC,CADiB,GAEjB,2BAAaN,gBAAKC,IAAL,CAAUF,SAAV,EAAqBG,OAArB,CAAb,CAFJ;;AAIA,MAAI,CAACI,YAAL,EAAmB;AACjB,WAAO,IAAP;AACD;;AAED,QAAMd,QAAQ,GAAG,2BAAac,YAAb,CAAjB;AAEA,QAAMC,WAAW,GAAGV,UAAU,CAACU,WAAX,IAA0BhB,cAAc,CAACC,QAAD,CAA5D;;AAEA,MAAI,CAACe,WAAL,EAAkB;AAChB,UAAM,IAAIC,KAAJ,CAAW,6BAA4BF,YAAa,EAApD,CAAN;AACD;;AAED,QAAMG,aAAa,GACjBZ,UAAU,CAACY,aAAX,IAA4BF,WAAW,CAACG,OAAZ,CAAoB,KAApB,EAA2BV,gBAAKW,GAAhC,CAD9B;;AAGA,QAAMC,YAAY,GAAGZ,gBAAKC,IAAL,CACnBF,SADmB,EAEnBF,UAAU,CAACe,YAAX,IACEZ,gBAAKC,IAAL,CAAUC,OAAV,EAAoB,iBAAgBO,aAAc,uBAAlD,CAHiB,CAArB;;AAMA,QAAMI,WAAW,GAAGb,gBAAKC,IAAL,CAClBF,SADkB,EAElBF,UAAU,CAACgB,WAAX,IACEb,gBAAKC,IAAL,CAAUC,OAAV,EAAmB,kCAAnB,CAHgB,CAApB;;AAMA,QAAMY,kBAAkB,GAAGd,gBAAKC,IAAL,CACzBF,SADyB,EAEzBF,UAAU,CAACiB,kBAAX,IAAiC,iBAFR,CAA3B;;AAKA,QAAMC,UAAU,GAAGf,gBAAKC,IAAL,CACjBF,SADiB,EAEjBF,UAAU,CAACkB,UAAX,IAAyBf,gBAAKC,IAAL,CAAUC,OAAV,EAAmB,kBAAnB,CAFR,CAAnB;;AAKA,QAAMc,eAAe,GAAGhB,gBAAKC,IAAL,CACtBF,SADsB,EAEtBF,UAAU,CAACmB,eAAX,IAA8B,cAFR,CAAxB;;AAKA,QAAMC,uBAAuB,GAAGpB,UAAU,CAACoB,uBAA3C;AAEA,SAAO;AACLlB,IAAAA,SADK;AAELK,IAAAA,MAFK;AAGLc,IAAAA,MAAM,EAAEtB,IAHH;AAILiB,IAAAA,WAJK;AAKLP,IAAAA,YALK;AAMLU,IAAAA,eANK;AAOLF,IAAAA,kBAPK;AAQLC,IAAAA,UARK;AASLH,IAAAA,YATK;AAULL,IAAAA,WAVK;AAWLE,IAAAA,aAXK;AAYLP,IAAAA,OAZK;AAaLe,IAAAA;AAbK,GAAP;AAeD;;AAED,SAASd,UAAT,CAAoBJ,SAApB,EAAuCoB,iBAAvC,EAA8E;AAC5E,MAAIjB,OAAO,GAAG,EAAd;;AACA,MACE,OAAOiB,iBAAP,KAA6B,QAA7B,IACAC,cAAGC,UAAH,CAAcrB,gBAAKC,IAAL,CAAUF,SAAV,EAAqBoB,iBAArB,CAAd,CAFF,EAGE;AACAjB,IAAAA,OAAO,GAAGiB,iBAAV;AACD,GALD,MAKO,IAAIC,cAAGC,UAAH,CAAcrB,gBAAKC,IAAL,CAAUF,SAAV,EAAqB,KAArB,CAAd,CAAJ,EAAgD;AACrDG,IAAAA,OAAO,GAAG,KAAV;AACD;;AACD,SAAOA,OAAP;AACD;AAED;AACA;AACA;AACA;;;AACO,SAASoB,gBAAT,CACL1B,IADK,EAELC,UAAmC,GAAG,EAFjC,EAGL;AACA,QAAMC,GAAG,GAAGD,UAAU,CAACE,SAAX,IAAwB,6BAAeH,IAAf,CAApC;;AAEA,MAAI,CAACE,GAAL,EAAU;AACR,WAAO,IAAP;AACD;;AAED,QAAMC,SAAS,GAAGC,gBAAKC,IAAL,CAAUL,IAAV,EAAgBE,GAAhB,CAAlB;;AACA,QAAMQ,YAAY,GAAGT,UAAU,CAACS,YAAX,GACjBN,gBAAKC,IAAL,CAAUF,SAAV,EAAqBF,UAAU,CAACS,YAAhC,CADiB,GAEjB,2BAAaP,SAAb,CAFJ;;AAIA,MAAI,CAACO,YAAL,EAAmB;AACjB,WAAO,IAAP;AACD;;AAED,QAAMd,QAAQ,GAAG,2BAAac,YAAb,CAAjB;AACA,QAAMC,WAAW,GAAGV,UAAU,CAACU,WAAX,IAA0BhB,cAAc,CAACC,QAAD,CAA5D;AACA,QAAM+B,gBAAgB,GAAG,mCAAqBxB,SAArB,CAAzB;AAEA;AACF;AACA;;AACE,MAAI,CAACwB,gBAAL,EAAuB;AACrB,WAAO,IAAP;AACD;;AAED,QAAMC,iBAAiB,GACrB3B,UAAU,CAAC2B,iBAAX,IACC,UAASjB,WAAY,IAAGgB,gBAAiB,GAF5C;AAIA,QAAME,eAAe,GACnB5B,UAAU,CAAC4B,eAAX,IAA+B,OAAMF,gBAAiB,IADxD;AAGA,QAAMG,UAAU,GAAG7B,UAAU,CAAC6B,UAAX,IAAyB,EAA5C;AACA,QAAMT,uBAAuB,GAAGpB,UAAU,CAACoB,uBAA3C;AAEA,SAAO;AACLlB,IAAAA,SADK;AAELmB,IAAAA,MAAM,EAAEtB,IAFH;AAGL4B,IAAAA,iBAHK;AAILC,IAAAA,eAJK;AAKLC,IAAAA,UALK;AAMLT,IAAAA;AANK,GAAP;AAQD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport fs from 'fs';\nimport findAndroidDir from './findAndroidDir';\nimport findManifest from './findManifest';\nimport findPackageClassName from './findPackageClassName';\nimport readManifest from './readManifest';\nimport {\n  AndroidProjectParams,\n  AndroidDependencyParams,\n  AndroidProjectConfig,\n} from '@react-native-community/cli-types';\nimport {XmlDocument} from 'xmldoc';\n\nconst getPackageName = (manifest: XmlDocument) => manifest.attr.package;\n\n/**\n * Gets android project config by analyzing given folder and taking some\n * defaults specified by user into consideration\n */\nexport function projectConfig(\n  root: string,\n  userConfig: AndroidProjectParams = {},\n): AndroidProjectConfig | null {\n  const src = userConfig.sourceDir || findAndroidDir(root);\n\n  if (!src) {\n    return null;\n  }\n\n  const sourceDir = path.join(root, src);\n  const appName = getAppName(sourceDir, userConfig.appName);\n  const isFlat = sourceDir.indexOf('app') === -1;\n  const manifestPath = userConfig.manifestPath\n    ? path.join(sourceDir, userConfig.manifestPath)\n    : findManifest(path.join(sourceDir, appName));\n\n  if (!manifestPath) {\n    return null;\n  }\n\n  const manifest = readManifest(manifestPath);\n\n  const packageName = userConfig.packageName || getPackageName(manifest);\n\n  if (!packageName) {\n    throw new Error(`Package name not found in ${manifestPath}`);\n  }\n\n  const packageFolder =\n    userConfig.packageFolder || packageName.replace(/\\./g, path.sep);\n\n  const mainFilePath = path.join(\n    sourceDir,\n    userConfig.mainFilePath ||\n      path.join(appName, `src/main/java/${packageFolder}/MainApplication.java`),\n  );\n\n  const stringsPath = path.join(\n    sourceDir,\n    userConfig.stringsPath ||\n      path.join(appName, '/src/main/res/values/strings.xml'),\n  );\n\n  const settingsGradlePath = path.join(\n    sourceDir,\n    userConfig.settingsGradlePath || 'settings.gradle',\n  );\n\n  const assetsPath = path.join(\n    sourceDir,\n    userConfig.assetsPath || path.join(appName, '/src/main/assets'),\n  );\n\n  const buildGradlePath = path.join(\n    sourceDir,\n    userConfig.buildGradlePath || 'build.gradle',\n  );\n\n  const dependencyConfiguration = userConfig.dependencyConfiguration;\n\n  return {\n    sourceDir,\n    isFlat,\n    folder: root,\n    stringsPath,\n    manifestPath,\n    buildGradlePath,\n    settingsGradlePath,\n    assetsPath,\n    mainFilePath,\n    packageName,\n    packageFolder,\n    appName,\n    dependencyConfiguration,\n  };\n}\n\nfunction getAppName(sourceDir: string, userConfigAppName: string | undefined) {\n  let appName = '';\n  if (\n    typeof userConfigAppName === 'string' &&\n    fs.existsSync(path.join(sourceDir, userConfigAppName))\n  ) {\n    appName = userConfigAppName;\n  } else if (fs.existsSync(path.join(sourceDir, 'app'))) {\n    appName = 'app';\n  }\n  return appName;\n}\n\n/**\n * Same as projectConfigAndroid except it returns\n * different config that applies to packages only\n */\nexport function dependencyConfig(\n  root: string,\n  userConfig: AndroidDependencyParams = {},\n) {\n  const src = userConfig.sourceDir || findAndroidDir(root);\n\n  if (!src) {\n    return null;\n  }\n\n  const sourceDir = path.join(root, src);\n  const manifestPath = userConfig.manifestPath\n    ? path.join(sourceDir, userConfig.manifestPath)\n    : findManifest(sourceDir);\n\n  if (!manifestPath) {\n    return null;\n  }\n\n  const manifest = readManifest(manifestPath);\n  const packageName = userConfig.packageName || getPackageName(manifest);\n  const packageClassName = findPackageClassName(sourceDir);\n\n  /**\n   * This module has no package to export\n   */\n  if (!packageClassName) {\n    return null;\n  }\n\n  const packageImportPath =\n    userConfig.packageImportPath ||\n    `import ${packageName}.${packageClassName};`;\n\n  const packageInstance =\n    userConfig.packageInstance || `new ${packageClassName}()`;\n\n  const buildTypes = userConfig.buildTypes || [];\n  const dependencyConfiguration = userConfig.dependencyConfiguration;\n\n  return {\n    sourceDir,\n    folder: root,\n    packageImportPath,\n    packageInstance,\n    buildTypes,\n    dependencyConfiguration,\n  };\n}\n"]}