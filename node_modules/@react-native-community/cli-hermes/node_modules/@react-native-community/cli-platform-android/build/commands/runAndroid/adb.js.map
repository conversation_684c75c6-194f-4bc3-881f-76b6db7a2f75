{"version": 3, "sources": ["../../../src/commands/runAndroid/adb.ts"], "names": ["parseDevicesResult", "result", "devices", "lines", "trim", "split", "i", "length", "words", "filter", "w", "push", "getDevices", "adbPath", "devicesResult", "toString", "e", "getAvailableCPUs", "device", "baseArgs", "cpus", "concat", "getCPU"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AARA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACA;AACA;AACA,SAASA,kBAAT,CAA4BC,MAA5B,EAA2D;AACzD,MAAI,CAACA,MAAL,EAAa;AACX,WAAO,EAAP;AACD;;AAED,QAAMC,OAAO,GAAG,EAAhB;AACA,QAAMC,KAAK,GAAGF,MAAM,CAACG,IAAP,GAAcC,KAAd,CAAoB,OAApB,CAAd;;AAEA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,KAAK,CAACI,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;AACrC,UAAME,KAAK,GAAGL,KAAK,CAACG,CAAD,CAAL,CAASD,KAAT,CAAe,SAAf,EAA0BI,MAA1B,CAAkCC,CAAD,IAAOA,CAAC,KAAK,EAA9C,CAAd;;AAEA,QAAIF,KAAK,CAAC,CAAD,CAAL,KAAa,QAAjB,EAA2B;AACzBN,MAAAA,OAAO,CAACS,IAAR,CAAaH,KAAK,CAAC,CAAD,CAAlB;AACD;AACF;;AACD,SAAON,OAAP;AACD;AAED;AACA;AACA;;;AACA,SAASU,UAAT,CAAoBC,OAApB,EAAoD;AAClD,MAAI;AACF,UAAMC,aAAa,GAAG,+BAAU,GAAED,OAAQ,UAApB,CAAtB;AACA,WAAOb,kBAAkB,CAACc,aAAa,CAACC,QAAd,EAAD,CAAzB;AACD,GAHD,CAGE,OAAOC,CAAP,EAAU;AACV,WAAO,EAAP;AACD;AACF;AAED;AACA;AACA;;;AACA,SAASC,gBAAT,CAA0BJ,OAA1B,EAA2CK,MAA3C,EAA0E;AACxE,MAAI;AACF,UAAMC,QAAQ,GAAG,CAAC,IAAD,EAAOD,MAAP,EAAe,OAAf,EAAwB,SAAxB,CAAjB;AAEA,QAAIE,IAAI,GAAG,mCACTP,OADS,EAETM,QAAQ,CAACE,MAAT,CAAgB,CAAC,wBAAD,CAAhB,CAFS,EAGTN,QAHS,EAAX,CAHE,CAQF;;AACA,QAAI,CAACK,IAAD,IAASA,IAAI,CAAChB,IAAL,GAAYG,MAAZ,KAAuB,CAApC,EAAuC;AACrCa,MAAAA,IAAI,GAAG,mCACLP,OADK,EAELM,QAAQ,CAACE,MAAT,CAAgB,CAAC,oBAAD,CAAhB,CAFK,EAGLN,QAHK,EAAP;AAID;;AAED,WAAO,CAACK,IAAI,IAAI,EAAT,EAAahB,IAAb,GAAoBC,KAApB,CAA0B,GAA1B,CAAP;AACD,GAjBD,CAiBE,OAAOW,CAAP,EAAU;AACV,WAAO,EAAP;AACD;AACF;AAED;AACA;AACA;;;AACA,SAASM,MAAT,CAAgBT,OAAhB,EAAiCK,MAAjC,EAAgE;AAC9D,MAAI;AACF,UAAME,IAAI,GAAG,mCAAaP,OAAb,EAAsB,CACjC,IADiC,EAEjCK,MAFiC,EAGjC,OAHiC,EAIjC,SAJiC,EAKjC,oBALiC,CAAtB,EAOVH,QAPU,GAQVX,IARU,EAAb;AAUA,WAAOgB,IAAI,CAACb,MAAL,GAAc,CAAd,GAAkBa,IAAlB,GAAyB,IAAhC;AACD,GAZD,CAYE,OAAOJ,CAAP,EAAU;AACV,WAAO,IAAP;AACD;AACF;;eAEc;AACbJ,EAAAA,UADa;AAEbK,EAAAA,gBAFa;AAGbK,EAAAA;AAHa,C", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {execSync, execFileSync} from 'child_process';\n\n/**\n * Parses the output of the 'adb devices' command\n */\nfunction parseDevicesResult(result: string): Array<string> {\n  if (!result) {\n    return [];\n  }\n\n  const devices = [];\n  const lines = result.trim().split(/\\r?\\n/);\n\n  for (let i = 0; i < lines.length; i++) {\n    const words = lines[i].split(/[ ,\\t]+/).filter((w) => w !== '');\n\n    if (words[1] === 'device') {\n      devices.push(words[0]);\n    }\n  }\n  return devices;\n}\n\n/**\n * Executes the commands needed to get a list of devices from ADB\n */\nfunction getDevices(adbPath: string): Array<string> {\n  try {\n    const devicesResult = execSync(`${adbPath} devices`);\n    return parseDevicesResult(devicesResult.toString());\n  } catch (e) {\n    return [];\n  }\n}\n\n/**\n * Gets available CPUs of devices from ADB\n */\nfunction getAvailableCPUs(adbPath: string, device: string): Array<string> {\n  try {\n    const baseArgs = ['-s', device, 'shell', 'getprop'];\n\n    let cpus = execFileSync(\n      adbPath,\n      baseArgs.concat(['ro.product.cpu.abilist']),\n    ).toString();\n\n    // pre-Lollipop\n    if (!cpus || cpus.trim().length === 0) {\n      cpus = execFileSync(\n        adbPath,\n        baseArgs.concat(['ro.product.cpu.abi']),\n      ).toString();\n    }\n\n    return (cpus || '').trim().split(',');\n  } catch (e) {\n    return [];\n  }\n}\n\n/**\n * Gets the CPU architecture of a device from ADB\n */\nfunction getCPU(adbPath: string, device: string): string | null {\n  try {\n    const cpus = execFileSync(adbPath, [\n      '-s',\n      device,\n      'shell',\n      'getprop',\n      'ro.product.cpu.abi',\n    ])\n      .toString()\n      .trim();\n\n    return cpus.length > 0 ? cpus : null;\n  } catch (e) {\n    return null;\n  }\n}\n\nexport default {\n  getDevices,\n  getAvailableCPUs,\n  getCPU,\n};\n"]}