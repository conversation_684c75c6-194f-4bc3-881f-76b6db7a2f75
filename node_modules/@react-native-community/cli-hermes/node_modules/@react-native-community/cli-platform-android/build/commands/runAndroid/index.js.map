{"version": 3, "sources": ["../../../src/commands/runAndroid/index.ts"], "names": ["displayWarnings", "config", "args", "appFolder", "logger", "warn", "root", "runAndroid", "_argv", "androidProject", "jetifier", "info", "chalk", "bold", "dim", "require", "resolve", "stdio", "error", "CLIError", "packager", "buildAndRun", "port", "then", "result", "startServerInNewWindow", "terminal", "reactNativePath", "message", "process", "chdir", "sourceDir", "cmd", "platform", "startsWith", "packageName", "adbPath", "deviceId", "runOnSpecificDevice", "gradlew", "devices", "adb", "getDevices", "length", "indexOf", "buildApk", "installAndLaunchOnDevice", "grad<PERSON><PERSON><PERSON><PERSON>", "debug", "join", "execa", "sync", "cwd", "tryInstallAppOnDevice", "device", "appName", "variant", "toLowerCase", "buildDirectory", "apkFile", "getInstallApkName", "pathToApk", "adbArgs", "availableCPUs", "getAvailableCPUs", "availableCPU", "concat", "apkName", "fs", "existsSync", "selected<PERSON><PERSON><PERSON>", "isWindows", "test", "scriptFile", "packagerEnvFilename", "portExportContent", "launchPackagerScript", "path", "scriptsDir", "dirname", "packagerEnvFile", "procConfig", "writeFileSync", "encoding", "flag", "detached", "name", "description", "func", "options", "default", "env", "RCT_METRO_PORT", "parse", "Number", "val", "split"], "mappings": ";;;;;;;AAOA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAMA;;AACA;;;;AAxBA;AACA;AACA;AACA;AACA;AACA;AACA;AAoBA,SAASA,eAAT,CAAyBC,MAAzB,EAAyCC,IAAzC,EAAsD;AACpD,4CAA4BD,MAA5B;;AACA,MAAIC,IAAI,CAACC,SAAT,EAAoB;AAClBC,uBAAOC,IAAP,CACE,uGADF;AAGD;;AACD,MAAIH,IAAI,CAACI,IAAT,EAAe;AACbF,uBAAOC,IAAP,CACE,iJADF;AAGD;AACF;;AAoBD;AACA;AACA;AACA,eAAeE,UAAf,CAA0BC,KAA1B,EAAgDP,MAAhD,EAAgEC,IAAhE,EAA6E;AAC3EF,EAAAA,eAAe,CAACC,MAAD,EAASC,IAAT,CAAf;AACA,QAAMO,cAAc,GAAG,0CAAkBR,MAAlB,CAAvB;;AAEA,MAAIC,IAAI,CAACQ,QAAT,EAAmB;AACjBN,uBAAOO,IAAP,CACG,WAAUC,iBAAMC,IAAN,CACT,UADS,CAET,sCAAqCD,iBAAME,GAAN,CACrC,gDADqC,CAErC,EALJ;;AAQA,QAAI;AACF,YAAM,sBAAMC,OAAO,CAACC,OAAR,CAAgB,qBAAhB,CAAN,EAA8C;AAACC,QAAAA,KAAK,EAAE;AAAR,OAA9C,CAAN;AACD,KAFD,CAEE,OAAOC,KAAP,EAAc;AACd,YAAM,KAAIC,oBAAJ,EAAa,yBAAb,EAAwCD,KAAxC,CAAN;AACD;AACF;;AAED,MAAI,CAAChB,IAAI,CAACkB,QAAV,EAAoB;AAClB,WAAOC,WAAW,CAACnB,IAAD,EAAOO,cAAP,CAAlB;AACD;;AAED,SAAO,mCAAkBP,IAAI,CAACoB,IAAvB,EAA6BC,IAA7B,CAAmCC,MAAD,IAAoB;AAC3D,QAAIA,MAAM,KAAK,SAAf,EAA0B;AACxBpB,yBAAOO,IAAP,CAAY,4BAAZ;AACD,KAFD,MAEO,IAAIa,MAAM,KAAK,cAAf,EAA+B;AACpCpB,yBAAOC,IAAP,CAAY,oDAAZ;AACD,KAFM,MAEA;AACL;AACAD,yBAAOO,IAAP,CAAY,uBAAZ;;AACA,UAAI;AACFc,QAAAA,sBAAsB,CACpBvB,IAAI,CAACoB,IADe,EAEpBpB,IAAI,CAACwB,QAFe,EAGpBzB,MAAM,CAAC0B,eAHa,CAAtB;AAKD,OAND,CAME,OAAOT,KAAP,EAAc;AACdd,2BAAOC,IAAP,CACG,+GAA8Ga,KAAK,CAACU,OAAQ,EAD/H;AAGD;AACF;;AACD,WAAOP,WAAW,CAACnB,IAAD,EAAOO,cAAP,CAAlB;AACD,GArBM,CAAP;AAsBD,C,CAED;;;AACA,SAASY,WAAT,CAAqBnB,IAArB,EAAkCO,cAAlC,EAAkE;AAChEoB,EAAAA,OAAO,CAACC,KAAR,CAAcrB,cAAc,CAACsB,SAA7B;AACA,QAAMC,GAAG,GAAGH,OAAO,CAACI,QAAR,CAAiBC,UAAjB,CAA4B,KAA5B,IAAqC,aAArC,GAAqD,WAAjE;AAEA,QAAM;AAAC/B,IAAAA;AAAD,MAAcD,IAApB;AACA,QAAMiC,WAAW,GAAG,uCAAe1B,cAAf,EAA+BN,SAA/B,CAApB;AAEA,QAAMiC,OAAO,GAAG,0BAAhB;;AACA,MAAIlC,IAAI,CAACmC,QAAT,EAAmB;AACjB,WAAOC,mBAAmB,CAACpC,IAAD,EAAO8B,GAAP,EAAYG,WAAZ,EAAyBC,OAAzB,EAAkC3B,cAAlC,CAA1B;AACD,GAFD,MAEO;AACL,WAAO,8BAAgBP,IAAhB,EAAsB8B,GAAtB,EAA2BG,WAA3B,EAAwCC,OAAxC,EAAiD3B,cAAjD,CAAP;AACD;AACF;;AAED,SAAS6B,mBAAT,CACEpC,IADF,EAEEqC,OAFF,EAGEJ,WAHF,EAIEC,OAJF,EAKE3B,cALF,EAME;AACA,QAAM+B,OAAO,GAAGC,aAAIC,UAAJ,CAAeN,OAAf,CAAhB;;AACA,QAAM;AAACC,IAAAA;AAAD,MAAanC,IAAnB;;AACA,MAAIsC,OAAO,CAACG,MAAR,GAAiB,CAAjB,IAAsBN,QAA1B,EAAoC;AAClC,QAAIG,OAAO,CAACI,OAAR,CAAgBP,QAAhB,MAA8B,CAAC,CAAnC,EAAsC;AACpCQ,MAAAA,QAAQ,CAACN,OAAD,EAAU9B,cAAc,CAACsB,SAAzB,CAAR;AACAe,MAAAA,wBAAwB,CACtB5C,IADsB,EAEtBmC,QAFsB,EAGtBF,WAHsB,EAItBC,OAJsB,EAKtB3B,cALsB,CAAxB;AAOD,KATD,MASO;AACLL,yBAAOc,KAAP,CACG,uCAAsCmB,QAAS,wCADlD,EAEE,GAAGG,OAFL;AAID;AACF,GAhBD,MAgBO;AACLpC,uBAAOc,KAAP,CAAa,0CAAb;AACD;AACF;;AAED,SAAS2B,QAAT,CAAkBN,OAAlB,EAAmCR,SAAnC,EAAsD;AACpD,MAAI;AACF;AACA,UAAMgB,UAAU,GAAG,CAAC,OAAD,EAAU,IAAV,EAAgB,MAAhB,CAAnB;;AACA3C,uBAAOO,IAAP,CAAY,qBAAZ;;AACAP,uBAAO4C,KAAP,CAAc,oBAAmBT,OAAQ,IAAGQ,UAAU,CAACE,IAAX,CAAgB,GAAhB,CAAqB,GAAjE;;AACAC,qBAAMC,IAAN,CAAWZ,OAAX,EAAoBQ,UAApB,EAAgC;AAAC9B,MAAAA,KAAK,EAAE,SAAR;AAAmBmC,MAAAA,GAAG,EAAErB;AAAxB,KAAhC;AACD,GAND,CAME,OAAOb,KAAP,EAAc;AACd,UAAM,KAAIC,oBAAJ,EAAa,0BAAb,EAAyCD,KAAzC,CAAN;AACD;AACF;;AAED,SAASmC,qBAAT,CACEnD,IADF,EAEEkC,OAFF,EAGEkB,MAHF,EAIE7C,cAJF,EAKE;AACA,MAAI;AACF;AACA,UAAM;AAAC8C,MAAAA,OAAD;AAAUxB,MAAAA;AAAV,QAAuBtB,cAA7B;AACA,UAAM;AAACN,MAAAA;AAAD,QAAcD,IAApB;AACA,UAAMsD,OAAO,GAAGtD,IAAI,CAACsD,OAAL,CAAaC,WAAb,EAAhB;AACA,UAAMC,cAAc,GAAI,GAAE3B,SAAU,IAAGwB,OAAQ,sBAAqBC,OAAQ,EAA5E;AACA,UAAMG,OAAO,GAAGC,iBAAiB,CAC/BzD,SAAS,IAAIoD,OADkB,EACT;AACtBnB,IAAAA,OAF+B,EAG/BoB,OAH+B,EAI/BF,MAJ+B,EAK/BI,cAL+B,CAAjC;AAQA,UAAMG,SAAS,GAAI,GAAEH,cAAe,IAAGC,OAAQ,EAA/C;AACA,UAAMG,OAAO,GAAG,CAAC,IAAD,EAAOR,MAAP,EAAe,SAAf,EAA0B,IAA1B,EAAgC,IAAhC,EAAsCO,SAAtC,CAAhB;;AACAzD,uBAAOO,IAAP,CAAa,qCAAoC2C,MAAO,MAAxD;;AACAlD,uBAAO4C,KAAP,CACG,yCAAwCM,MAAO,kBAAiBO,SAAU,GAD7E;;AAGAX,qBAAMC,IAAN,CAAWf,OAAX,EAAoB0B,OAApB,EAA6B;AAAC7C,MAAAA,KAAK,EAAE;AAAR,KAA7B;AACD,GArBD,CAqBE,OAAOC,KAAP,EAAc;AACd,UAAM,KAAIC,oBAAJ,EAAa,0CAAb,EAAyDD,KAAzD,CAAN;AACD;AACF;;AAED,SAAS0C,iBAAT,CACEL,OADF,EAEEnB,OAFF,EAGEoB,OAHF,EAIEF,MAJF,EAKEI,cALF,EAME;AACA,QAAMK,aAAa,GAAGtB,aAAIuB,gBAAJ,CAAqB5B,OAArB,EAA8BkB,MAA9B,CAAtB,CADA,CAGA;;;AACA,OAAK,MAAMW,YAAX,IAA2BF,aAAa,CAACG,MAAd,CAAqB,WAArB,CAA3B,EAA8D;AAC5D,UAAMC,OAAO,GAAI,GAAEZ,OAAQ,IAAGU,YAAa,IAAGT,OAAQ,MAAtD;;AACA,QAAIY,cAAGC,UAAH,CAAe,GAAEX,cAAe,IAAGS,OAAQ,EAA3C,CAAJ,EAAmD;AACjD,aAAOA,OAAP;AACD;AACF,GATD,CAWA;;;AACA,QAAMA,OAAO,GAAI,GAAEZ,OAAQ,IAAGC,OAAQ,MAAtC;;AACA,MAAIY,cAAGC,UAAH,CAAe,GAAEX,cAAe,IAAGS,OAAQ,EAA3C,CAAJ,EAAmD;AACjD,WAAOA,OAAP;AACD;;AAED,QAAM,KAAIhD,oBAAJ,EAAa,8CAAb,CAAN;AACD;;AAED,SAAS2B,wBAAT,CACE5C,IADF,EAEEoE,cAFF,EAGEnC,WAHF,EAIEC,OAJF,EAKE3B,cALF,EAME;AACA,iCAAiBP,IAAI,CAACoB,IAAtB,EAA4BgD,cAA5B;AACAjB,EAAAA,qBAAqB,CAACnD,IAAD,EAAOkC,OAAP,EAAgBkC,cAAhB,EAAgC7D,cAAhC,CAArB;AACA,qCAAqB6D,cAArB,EAAqCnC,WAArC,EAAkDC,OAAlD,EAA2DlC,IAA3D;AACD;;AAED,SAASuB,sBAAT,CACEH,IADF,EAEEI,QAFF,EAGEC,eAHF,EAIE;AACA;AACF;AACA;AACE,QAAM4C,SAAS,GAAG,OAAOC,IAAP,CAAY3C,OAAO,CAACI,QAApB,CAAlB;AACA,QAAMwC,UAAU,GAAGF,SAAS,GACxB,oBADwB,GAExB,wBAFJ;AAGA,QAAMG,mBAAmB,GAAGH,SAAS,GAAG,eAAH,GAAqB,eAA1D;AACA,QAAMI,iBAAiB,GAAGJ,SAAS,GAC9B,sBAAqBjD,IAAK,EADI,GAE9B,yBAAwBA,IAAK,EAFlC;AAIA;AACF;AACA;;AACE,QAAMsD,oBAAoB,GAAGC,gBAAK5B,IAAL,CAC3BtB,eAD2B,EAE1B,WAAU8C,UAAW,EAFK,CAA7B;AAKA;AACF;AACA;AACA;;;AACE,QAAMK,UAAU,GAAGD,gBAAKE,OAAL,CAAaH,oBAAb,CAAnB;;AACA,QAAMI,eAAe,GAAGH,gBAAK5B,IAAL,CAAU6B,UAAV,EAAsBJ,mBAAtB,CAAxB;;AACA,QAAMO,UAA6B,GAAG;AAAC7B,IAAAA,GAAG,EAAE0B;AAAN,GAAtC;AAEA;AACF;AACA;;AACEV,gBAAGc,aAAH,CAAiBF,eAAjB,EAAkCL,iBAAlC,EAAqD;AACnDQ,IAAAA,QAAQ,EAAE,MADyC;AAEnDC,IAAAA,IAAI,EAAE;AAF6C,GAArD;;AAKA,MAAIvD,OAAO,CAACI,QAAR,KAAqB,QAAzB,EAAmC;AACjC,QAAI;AACF,aAAOiB,iBAAMC,IAAN,CACL,MADK,EAEL,CAAC,IAAD,EAAOzB,QAAP,EAAiBkD,oBAAjB,CAFK,EAGLK,UAHK,CAAP;AAKD,KAND,CAME,OAAO/D,KAAP,EAAc;AACd,aAAOgC,iBAAMC,IAAN,CAAW,MAAX,EAAmB,CAACyB,oBAAD,CAAnB,EAA2CK,UAA3C,CAAP;AACD;AACF;;AACD,MAAIpD,OAAO,CAACI,QAAR,KAAqB,OAAzB,EAAkC;AAChC,QAAI;AACF,aAAOiB,iBAAMC,IAAN,CAAWzB,QAAX,EAAqB,CAAC,IAAD,EAAQ,MAAKkD,oBAAqB,EAAlC,CAArB,EAA2D,EAChE,GAAGK,UAD6D;AAEhEI,QAAAA,QAAQ,EAAE;AAFsD,OAA3D,CAAP;AAID,KALD,CAKE,OAAOnE,KAAP,EAAc;AACd;AACA,aAAOgC,iBAAMC,IAAN,CAAW,IAAX,EAAiB,CAACyB,oBAAD,CAAjB,EAAyCK,UAAzC,CAAP;AACD;AACF;;AACD,MAAI,OAAOT,IAAP,CAAY3C,OAAO,CAACI,QAApB,CAAJ,EAAmC;AACjC;AACA,WAAO,sBAAM,SAAN,EAAiB,CAAC,IAAD,EAAO2C,oBAAP,CAAjB,EAA+C,EACpD,GAAGK,UADiD;AAEpDI,MAAAA,QAAQ,EAAE,IAF0C;AAGpDpE,MAAAA,KAAK,EAAE;AAH6C,KAA/C,CAAP;AAKD;;AACDb,qBAAOc,KAAP,CACG,+CAA8CW,OAAO,CAACI,QAAS,EADlE;;AAGA;AACD;;eAEc;AACbqD,EAAAA,IAAI,EAAE,aADO;AAEbC,EAAAA,WAAW,EACT,yEAHW;AAIbC,EAAAA,IAAI,EAAEjF,UAJO;AAKbkF,EAAAA,OAAO,EAAE,CACP;AACEH,IAAAA,IAAI,EAAE,iBADR;AAEEC,IAAAA,WAAW,EACT,0IAHJ;AAIEG,IAAAA,OAAO,EAAE;AAJX,GADO,EAOP;AACEJ,IAAAA,IAAI,EAAE,oBADR;AAEEC,IAAAA,WAAW,EAAE,kCAFf;AAGEG,IAAAA,OAAO,EAAE;AAHX,GAPO,EAYP;AACEJ,IAAAA,IAAI,EAAE,sBADR;AAEEC,IAAAA,WAAW,EACT;AAHJ,GAZO,EAiBP;AACED,IAAAA,IAAI,EAAE,kBADR;AAEEC,IAAAA,WAAW,EACT,oHAHJ;AAIEG,IAAAA,OAAO,EAAE;AAJX,GAjBO,EAuBP;AACEJ,IAAAA,IAAI,EAAE,wBADR;AAEEC,IAAAA,WAAW,EAAE,uDAFf;AAGEG,IAAAA,OAAO,EAAE;AAHX,GAvBO,EA4BP;AACEJ,IAAAA,IAAI,EAAE,0BADR;AAEEC,IAAAA,WAAW,EAAE,+BAFf;AAGEG,IAAAA,OAAO,EAAE;AAHX,GA5BO,EAiCP;AACEJ,IAAAA,IAAI,EAAE,qBADR;AAEEC,IAAAA,WAAW,EACT,2EACA;AAJJ,GAjCO,EAuCP;AACED,IAAAA,IAAI,EAAE,eADR;AAEEC,IAAAA,WAAW,EAAE;AAFf,GAvCO,EA2CP;AACED,IAAAA,IAAI,EAAE,iBADR;AAEEI,IAAAA,OAAO,EAAE7D,OAAO,CAAC8D,GAAR,CAAYC,cAAZ,IAA8B,IAFzC;AAGEC,IAAAA,KAAK,EAAEC;AAHT,GA3CO,EAgDP;AACER,IAAAA,IAAI,EAAE,qBADR;AAEEC,IAAAA,WAAW,EACT,+EAHJ;AAIEG,IAAAA,OAAO,EAAE;AAJX,GAhDO,EAsDP;AACEJ,IAAAA,IAAI,EAAE,gBADR;AAEEC,IAAAA,WAAW,EAAE,0DAFf;AAGEM,IAAAA,KAAK,EAAGE,GAAD,IAAiBA,GAAG,CAACC,KAAJ,CAAU,GAAV;AAH1B,GAtDO,EA2DP;AACEV,IAAAA,IAAI,EAAE,eADR;AAEEC,IAAAA,WAAW,EACT;AAHJ,GA3DO,EAgEP;AACED,IAAAA,IAAI,EAAE,oBADR;AAEEC,IAAAA,WAAW,EACT,mFAHJ;AAIEG,IAAAA,OAAO,EAAE;AAJX,GAhEO;AALI,C", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport path from 'path';\nimport execa from 'execa';\nimport chalk from 'chalk';\nimport fs from 'fs';\nimport {Config} from '@react-native-community/cli-types';\nimport adb from './adb';\nimport runOnAllDevices from './runOnAllDevices';\nimport tryRunAdbReverse from './tryRunAdbReverse';\nimport tryLaunchAppOnDevice from './tryLaunchAppOnDevice';\nimport getAdbPath from './getAdbPath';\nimport {\n  isPackagerRunning,\n  logger,\n  getDefaultUserTerminal,\n  CLIError,\n} from '@react-native-community/cli-tools';\nimport warnAboutManuallyLinkedLibs from '../../link/warnAboutManuallyLinkedLibs';\nimport {getAndroidProject, getPackageName} from '../../utils/getAndroidProject';\n\nfunction displayWarnings(config: Config, args: Flags) {\n  warnAboutManuallyLinkedLibs(config);\n  if (args.appFolder) {\n    logger.warn(\n      'Using deprecated \"--appFolder\" flag. Use \"project.android.appName\" in react-native.config.js instead.',\n    );\n  }\n  if (args.root) {\n    logger.warn(\n      'Using deprecated \"--root\" flag. App root is discovered automatically. Alternatively, set \"project.android.sourceDir\" in react-native.config.js.',\n    );\n  }\n}\n\nexport interface Flags {\n  tasks?: Array<string>;\n  root: string;\n  variant: string;\n  appFolder: string;\n  appId: string;\n  appIdSuffix: string;\n  mainActivity: string;\n  deviceId?: string;\n  packager: boolean;\n  port: number;\n  terminal: string;\n  jetifier: boolean;\n  activeArchOnly: boolean;\n}\n\ntype AndroidProject = NonNullable<Config['project']['android']>;\n\n/**\n * Starts the app on a connected Android emulator or device.\n */\nasync function runAndroid(_argv: Array<string>, config: Config, args: Flags) {\n  displayWarnings(config, args);\n  const androidProject = getAndroidProject(config);\n\n  if (args.jetifier) {\n    logger.info(\n      `Running ${chalk.bold(\n        'jetifier',\n      )} to migrate libraries to AndroidX. ${chalk.dim(\n        'You can disable it using \"--no-jetifier\" flag.',\n      )}`,\n    );\n\n    try {\n      await execa(require.resolve('jetifier/bin/jetify'), {stdio: 'inherit'});\n    } catch (error) {\n      throw new CLIError('Failed to run jetifier.', error);\n    }\n  }\n\n  if (!args.packager) {\n    return buildAndRun(args, androidProject);\n  }\n\n  return isPackagerRunning(args.port).then((result: string) => {\n    if (result === 'running') {\n      logger.info('JS server already running.');\n    } else if (result === 'unrecognized') {\n      logger.warn('JS server not recognized, continuing with build...');\n    } else {\n      // result == 'not_running'\n      logger.info('Starting JS server...');\n      try {\n        startServerInNewWindow(\n          args.port,\n          args.terminal,\n          config.reactNativePath,\n        );\n      } catch (error) {\n        logger.warn(\n          `Failed to automatically start the packager server. Please run \"react-native start\" manually. Error details: ${error.message}`,\n        );\n      }\n    }\n    return buildAndRun(args, androidProject);\n  });\n}\n\n// Builds the app and runs it on a connected emulator / device.\nfunction buildAndRun(args: Flags, androidProject: AndroidProject) {\n  process.chdir(androidProject.sourceDir);\n  const cmd = process.platform.startsWith('win') ? 'gradlew.bat' : './gradlew';\n\n  const {appFolder} = args;\n  const packageName = getPackageName(androidProject, appFolder);\n\n  const adbPath = getAdbPath();\n  if (args.deviceId) {\n    return runOnSpecificDevice(args, cmd, packageName, adbPath, androidProject);\n  } else {\n    return runOnAllDevices(args, cmd, packageName, adbPath, androidProject);\n  }\n}\n\nfunction runOnSpecificDevice(\n  args: Flags,\n  gradlew: 'gradlew.bat' | './gradlew',\n  packageName: string,\n  adbPath: string,\n  androidProject: AndroidProject,\n) {\n  const devices = adb.getDevices(adbPath);\n  const {deviceId} = args;\n  if (devices.length > 0 && deviceId) {\n    if (devices.indexOf(deviceId) !== -1) {\n      buildApk(gradlew, androidProject.sourceDir);\n      installAndLaunchOnDevice(\n        args,\n        deviceId,\n        packageName,\n        adbPath,\n        androidProject,\n      );\n    } else {\n      logger.error(\n        `Could not find device with the id: \"${deviceId}\". Please choose one of the following:`,\n        ...devices,\n      );\n    }\n  } else {\n    logger.error('No Android device or emulator connected.');\n  }\n}\n\nfunction buildApk(gradlew: string, sourceDir: string) {\n  try {\n    // using '-x lint' in order to ignore linting errors while building the apk\n    const gradleArgs = ['build', '-x', 'lint'];\n    logger.info('Building the app...');\n    logger.debug(`Running command \"${gradlew} ${gradleArgs.join(' ')}\"`);\n    execa.sync(gradlew, gradleArgs, {stdio: 'inherit', cwd: sourceDir});\n  } catch (error) {\n    throw new CLIError('Failed to build the app.', error);\n  }\n}\n\nfunction tryInstallAppOnDevice(\n  args: Flags,\n  adbPath: string,\n  device: string,\n  androidProject: AndroidProject,\n) {\n  try {\n    // \"app\" is usually the default value for Android apps with only 1 app\n    const {appName, sourceDir} = androidProject;\n    const {appFolder} = args;\n    const variant = args.variant.toLowerCase();\n    const buildDirectory = `${sourceDir}/${appName}/build/outputs/apk/${variant}`;\n    const apkFile = getInstallApkName(\n      appFolder || appName, // TODO: remove appFolder\n      adbPath,\n      variant,\n      device,\n      buildDirectory,\n    );\n\n    const pathToApk = `${buildDirectory}/${apkFile}`;\n    const adbArgs = ['-s', device, 'install', '-r', '-d', pathToApk];\n    logger.info(`Installing the app on the device \"${device}\"...`);\n    logger.debug(\n      `Running command \"cd android && adb -s ${device} install -r -d ${pathToApk}\"`,\n    );\n    execa.sync(adbPath, adbArgs, {stdio: 'inherit'});\n  } catch (error) {\n    throw new CLIError('Failed to install the app on the device.', error);\n  }\n}\n\nfunction getInstallApkName(\n  appName: string,\n  adbPath: string,\n  variant: string,\n  device: string,\n  buildDirectory: string,\n) {\n  const availableCPUs = adb.getAvailableCPUs(adbPath, device);\n\n  // check if there is an apk file like app-armeabi-v7a-debug.apk\n  for (const availableCPU of availableCPUs.concat('universal')) {\n    const apkName = `${appName}-${availableCPU}-${variant}.apk`;\n    if (fs.existsSync(`${buildDirectory}/${apkName}`)) {\n      return apkName;\n    }\n  }\n\n  // check if there is a default file like app-debug.apk\n  const apkName = `${appName}-${variant}.apk`;\n  if (fs.existsSync(`${buildDirectory}/${apkName}`)) {\n    return apkName;\n  }\n\n  throw new CLIError('Could not find the correct install APK file.');\n}\n\nfunction installAndLaunchOnDevice(\n  args: Flags,\n  selectedDevice: string,\n  packageName: string,\n  adbPath: string,\n  androidProject: AndroidProject,\n) {\n  tryRunAdbReverse(args.port, selectedDevice);\n  tryInstallAppOnDevice(args, adbPath, selectedDevice, androidProject);\n  tryLaunchAppOnDevice(selectedDevice, packageName, adbPath, args);\n}\n\nfunction startServerInNewWindow(\n  port: number,\n  terminal: string,\n  reactNativePath: string,\n) {\n  /**\n   * Set up OS-specific filenames and commands\n   */\n  const isWindows = /^win/.test(process.platform);\n  const scriptFile = isWindows\n    ? 'launchPackager.bat'\n    : 'launchPackager.command';\n  const packagerEnvFilename = isWindows ? '.packager.bat' : '.packager.env';\n  const portExportContent = isWindows\n    ? `set RCT_METRO_PORT=${port}`\n    : `export RCT_METRO_PORT=${port}`;\n\n  /**\n   * Set up the `.packager.(env|bat)` file to ensure the packager starts on the right port.\n   */\n  const launchPackagerScript = path.join(\n    reactNativePath,\n    `scripts/${scriptFile}`,\n  );\n\n  /**\n   * Set up the `launchpackager.(command|bat)` file.\n   * It lives next to `.packager.(bat|env)`\n   */\n  const scriptsDir = path.dirname(launchPackagerScript);\n  const packagerEnvFile = path.join(scriptsDir, packagerEnvFilename);\n  const procConfig: execa.SyncOptions = {cwd: scriptsDir};\n\n  /**\n   * Ensure we overwrite file by passing the `w` flag\n   */\n  fs.writeFileSync(packagerEnvFile, portExportContent, {\n    encoding: 'utf8',\n    flag: 'w',\n  });\n\n  if (process.platform === 'darwin') {\n    try {\n      return execa.sync(\n        'open',\n        ['-a', terminal, launchPackagerScript],\n        procConfig,\n      );\n    } catch (error) {\n      return execa.sync('open', [launchPackagerScript], procConfig);\n    }\n  }\n  if (process.platform === 'linux') {\n    try {\n      return execa.sync(terminal, ['-e', `sh ${launchPackagerScript}`], {\n        ...procConfig,\n        detached: true,\n      });\n    } catch (error) {\n      // By default, the child shell process will be attached to the parent\n      return execa.sync('sh', [launchPackagerScript], procConfig);\n    }\n  }\n  if (/^win/.test(process.platform)) {\n    // Awaiting this causes the CLI to hang indefinitely, so this must execute without await.\n    return execa('cmd.exe', ['/C', launchPackagerScript], {\n      ...procConfig,\n      detached: true,\n      stdio: 'ignore',\n    });\n  }\n  logger.error(\n    `Cannot start the packager. Unknown platform ${process.platform}`,\n  );\n  return;\n}\n\nexport default {\n  name: 'run-android',\n  description:\n    'builds your app and starts it on a connected Android emulator or device',\n  func: runAndroid,\n  options: [\n    {\n      name: '--root <string>',\n      description:\n        '[DEPRECATED - root is discovered automatically] Override the root directory for the android build (which contains the android directory)',\n      default: '',\n    },\n    {\n      name: '--variant <string>',\n      description: \"Specify your app's build variant\",\n      default: 'debug',\n    },\n    {\n      name: '--appFolder <string>',\n      description:\n        '[DEPRECATED – use \"project.android.appName\" in react-native.config.js] Specify a different application folder name for the android source. If not, we assume is \"app\"',\n    },\n    {\n      name: '--appId <string>',\n      description:\n        'Specify an applicationId to launch after build. If not specified, `package` from AndroidManifest.xml will be used.',\n      default: '',\n    },\n    {\n      name: '--appIdSuffix <string>',\n      description: 'Specify an applicationIdSuffix to launch after build.',\n      default: '',\n    },\n    {\n      name: '--main-activity <string>',\n      description: 'Name of the activity to start',\n      default: 'MainActivity',\n    },\n    {\n      name: '--deviceId <string>',\n      description:\n        'builds your app and starts it on a specific device/simulator with the ' +\n        'given device id (listed by running \"adb devices\" on the command line).',\n    },\n    {\n      name: '--no-packager',\n      description: 'Do not launch packager while building',\n    },\n    {\n      name: '--port <number>',\n      default: process.env.RCT_METRO_PORT || 8081,\n      parse: Number,\n    },\n    {\n      name: '--terminal <string>',\n      description:\n        'Launches the Metro Bundler in a new window using the specified terminal path.',\n      default: getDefaultUserTerminal(),\n    },\n    {\n      name: '--tasks <list>',\n      description: 'Run custom Gradle tasks. By default it\\'s \"installDebug\"',\n      parse: (val: string) => val.split(','),\n    },\n    {\n      name: '--no-jetifier',\n      description:\n        'Do not run \"jetifier\" – the AndroidX transition tool. By default it runs before Gradle to ease working with libraries that don\\'t support AndroidX yet. See more at: https://www.npmjs.com/package/jetifier.',\n    },\n    {\n      name: '--active-arch-only',\n      description:\n        'Build native libraries only for the current device architecture for debug builds.',\n      default: false,\n    },\n  ],\n};\n"]}