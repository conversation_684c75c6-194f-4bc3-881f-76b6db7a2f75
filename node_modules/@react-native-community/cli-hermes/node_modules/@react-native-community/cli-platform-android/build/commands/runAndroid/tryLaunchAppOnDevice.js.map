{"version": 3, "sources": ["../../../src/commands/runAndroid/tryLaunchAppOnDevice.ts"], "names": ["tryLaunchAppOnDevice", "device", "packageName", "adbPath", "args", "appId", "appIdSuffix", "packageNameWithSuffix", "filter", "Boolean", "join", "activityToLaunch", "mainActivity", "includes", "adbArgs", "unshift", "logger", "info", "debug", "execa", "sync", "stdio", "error", "CLIError"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA,SAASA,oBAAT,CACEC,MADF,EAEEC,WAFF,EAGEC,OAHF,EAIEC,IAJF,EAKE;AACA,QAAM;AAACC,IAAAA,KAAD;AAAQC,IAAAA;AAAR,MAAuBF,IAA7B;AACA,QAAMG,qBAAqB,GAAG,CAACF,KAAK,IAAIH,WAAV,EAAuBI,WAAvB,EAC3BE,MAD2B,CACpBC,OADoB,EAE3BC,IAF2B,CAEtB,GAFsB,CAA9B;AAIA,QAAMC,gBAAgB,GAAGP,IAAI,CAACQ,YAAL,CAAkBC,QAAlB,CAA2B,GAA3B,IACrBT,IAAI,CAACQ,YADgB,GAErB,CAACV,WAAD,EAAcE,IAAI,CAACQ,YAAnB,EAAiCJ,MAAjC,CAAwCC,OAAxC,EAAiDC,IAAjD,CAAsD,GAAtD,CAFJ;;AAIA,MAAI;AACF,UAAMI,OAAO,GAAG,CACd,OADc,EAEd,IAFc,EAGd,OAHc,EAId,IAJc,EAKb,GAAEP,qBAAsB,IAAGI,gBAAiB,EAL/B,CAAhB;;AAOA,QAAIV,MAAJ,EAAY;AACVa,MAAAA,OAAO,CAACC,OAAR,CAAgB,IAAhB,EAAsBd,MAAtB;;AACAe,yBAAOC,IAAP,CAAa,wBAAuBhB,MAAO,MAA3C;AACD,KAHD,MAGO;AACLe,yBAAOC,IAAP,CAAY,qBAAZ;AACD;;AACDD,uBAAOE,KAAP,CAAc,oBAAmBf,OAAQ,IAAGW,OAAO,CAACJ,IAAR,CAAa,GAAb,CAAkB,GAA9D;;AACAS,qBAAMC,IAAN,CAAWjB,OAAX,EAAoBW,OAApB,EAA6B;AAACO,MAAAA,KAAK,EAAE;AAAR,KAA7B;AACD,GAhBD,CAgBE,OAAOC,KAAP,EAAc;AACd,UAAM,KAAIC,oBAAJ,EAAa,0BAAb,EAAyCD,KAAzC,CAAN;AACD;AACF;;eAEctB,oB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport execa from 'execa';\nimport {Flags} from '.';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\n\nfunction tryLaunchAppOnDevice(\n  device: string | void,\n  packageName: string,\n  adbPath: string,\n  args: Flags,\n) {\n  const {appId, appIdSuffix} = args;\n  const packageNameWithSuffix = [appId || packageName, appIdSuffix]\n    .filter(Boolean)\n    .join('.');\n\n  const activityToLaunch = args.mainActivity.includes('.')\n    ? args.mainActivity\n    : [packageName, args.mainActivity].filter(Boolean).join('.');\n\n  try {\n    const adbArgs = [\n      'shell',\n      'am',\n      'start',\n      '-n',\n      `${packageNameWithSuffix}/${activityToLaunch}`,\n    ];\n    if (device) {\n      adbArgs.unshift('-s', device);\n      logger.info(`Starting the app on \"${device}\"...`);\n    } else {\n      logger.info('Starting the app...');\n    }\n    logger.debug(`Running command \"${adbPath} ${adbArgs.join(' ')}\"`);\n    execa.sync(adbPath, adbArgs, {stdio: 'inherit'});\n  } catch (error) {\n    throw new CLIError('Failed to start the app.', error);\n  }\n}\n\nexport default tryLaunchAppOnDevice;\n"]}