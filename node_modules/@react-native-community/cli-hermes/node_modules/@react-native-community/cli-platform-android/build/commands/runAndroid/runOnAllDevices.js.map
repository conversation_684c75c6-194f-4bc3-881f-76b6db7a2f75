{"version": 3, "sources": ["../../../src/commands/runAndroid/runOnAllDevices.ts"], "names": ["getTaskNames", "appName", "commands", "map", "command", "toPascalCase", "value", "toUpperCase", "slice", "runOnAllDevices", "args", "cmd", "packageName", "adbPath", "androidProject", "devices", "adb", "getDevices", "length", "logger", "info", "result", "success", "error", "chalk", "dim", "warn", "tasks", "variant", "grad<PERSON><PERSON><PERSON><PERSON>", "appFolder", "port", "push", "activeArchOnly", "architectures", "device", "getCPU", "filter", "arch", "join", "debug", "stdio", "cwd", "sourceDir", "createInstallError", "undefined", "for<PERSON>ach", "stderr", "toString", "docs", "message", "underline", "log", "includes", "bold", "CLIError"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;;AACA;;AACA;;;;AAfA;AACA;AACA;AACA;AACA;AACA;AACA;AAYA,SAASA,YAAT,CAAsBC,OAAtB,EAAuCC,QAAvC,EAA+E;AAC7E,SAAOD,OAAO,GACVC,QAAQ,CAACC,GAAT,CAAcC,OAAD,IAAc,GAAEH,OAAQ,IAAGG,OAAQ,EAAhD,CADU,GAEVF,QAFJ;AAGD;;AAED,SAASG,YAAT,CAAsBC,KAAtB,EAAqC;AACnC,SAAOA,KAAK,KAAK,EAAV,GAAeA,KAAK,CAAC,CAAD,CAAL,CAASC,WAAT,KAAyBD,KAAK,CAACE,KAAN,CAAY,CAAZ,CAAxC,GAAyDF,KAAhE;AACD;;AAID,eAAeG,eAAf,CACEC,IADF,EAEEC,GAFF,EAGEC,WAHF,EAIEC,OAJF,EAKEC,cALF,EAME;AACA,MAAIC,OAAO,GAAGC,aAAIC,UAAJ,CAAeJ,OAAf,CAAd;;AACA,MAAIE,OAAO,CAACG,MAAR,KAAmB,CAAvB,EAA0B;AACxBC,uBAAOC,IAAP,CAAY,uBAAZ;;AACA,UAAMC,MAAM,GAAG,MAAM,gCAAkBR,OAAlB,CAArB;;AACA,QAAIQ,MAAM,CAACC,OAAX,EAAoB;AAClBH,yBAAOC,IAAP,CAAY,iCAAZ;;AACAL,MAAAA,OAAO,GAAGC,aAAIC,UAAJ,CAAeJ,OAAf,CAAV;AACD,KAHD,MAGO;AACLM,yBAAOI,KAAP,CACG,sCAAqCC,iBAAMC,GAAN,CAAUJ,MAAM,CAACE,KAAP,IAAgB,EAA1B,CAA8B,GADtE;;AAGAJ,yBAAOO,IAAP,CACE,2FADF;AAGD;AACF;;AAED,MAAI;AACF,UAAMC,KAAK,GAAGjB,IAAI,CAACiB,KAAL,IAAc,CAAC,YAAYtB,YAAY,CAACK,IAAI,CAACkB,OAAN,CAAzB,CAA5B;AACA,UAAMC,UAAU,GAAG7B,YAAY,CAC7BU,IAAI,CAACoB,SAAL,IAAkBhB,cAAc,CAACb,OADJ,EAE7B0B,KAF6B,CAA/B;;AAKA,QAAIjB,IAAI,CAACqB,IAAL,IAAa,IAAjB,EAAuB;AACrBF,MAAAA,UAAU,CAACG,IAAX,CAAgB,gCAAgCtB,IAAI,CAACqB,IAArD;AACD;;AAED,QAAIrB,IAAI,CAACuB,cAAT,EAAyB;AACvB,YAAMC,aAAa,GAAGnB,OAAO,CAC1BZ,GADmB,CACdgC,MAAD,IAAY;AACf,eAAOnB,aAAIoB,MAAJ,CAAWvB,OAAX,EAAoBsB,MAApB,CAAP;AACD,OAHmB,EAInBE,MAJmB,CAIXC,IAAD,IAAUA,IAAI,IAAI,IAJN,CAAtB;;AAKA,UAAIJ,aAAa,CAAChB,MAAd,GAAuB,CAA3B,EAA8B;AAC5BC,2BAAOC,IAAP,CAAa,0BAAyBc,aAAa,CAACK,IAAd,CAAmB,IAAnB,CAAyB,EAA/D,EAD4B,CAE5B;AACA;;;AACAV,QAAAA,UAAU,CAACG,IAAX,CACE,qCAAqCE,aAAa,CAACK,IAAd,CAAmB,GAAnB,CADvC;AAGAV,QAAAA,UAAU,CAACG,IAAX,CACE,gCAAgCE,aAAa,CAACK,IAAd,CAAmB,GAAnB,CADlC;AAGD;AACF;;AAEDpB,uBAAOC,IAAP,CAAY,uBAAZ;;AACAD,uBAAOqB,KAAP,CACG,kCAAiC7B,GAAI,IAAGkB,UAAU,CAACU,IAAX,CAAgB,GAAhB,CAAqB,GADhE;;AAIA,UAAM,sBAAM5B,GAAN,EAAWkB,UAAX,EAAuB;AAC3BY,MAAAA,KAAK,EAAE,CAAC,SAAD,EAAY,SAAZ,EAAuB,MAAvB,CADoB;AAE3BC,MAAAA,GAAG,EAAE5B,cAAc,CAAC6B;AAFO,KAAvB,CAAN;AAID,GAvCD,CAuCE,OAAOpB,KAAP,EAAc;AACd,UAAMqB,kBAAkB,CAACrB,KAAD,CAAxB;AACD;;AAED,GAACR,OAAO,CAACG,MAAR,GAAiB,CAAjB,GAAqBH,OAArB,GAA+B,CAAC8B,SAAD,CAAhC,EAA6CC,OAA7C,CACGX,MAAD,IAA2B;AACzB,mCAAiBzB,IAAI,CAACqB,IAAtB,EAA4BI,MAA5B;AACA,uCAAqBA,MAArB,EAA6BvB,WAA7B,EAA0CC,OAA1C,EAAmDH,IAAnD;AACD,GAJH;AAMD;;AAED,SAASkC,kBAAT,CAA4BrB,KAA5B,EAA6D;AAC3D,QAAMwB,MAAM,GAAG,CAACxB,KAAK,CAACwB,MAAN,IAAgB,EAAjB,EAAqBC,QAArB,EAAf;AACA,QAAMC,IAAI,GAAG,gDAAb;AACA,MAAIC,OAAO,GAAI,kEAAiE1B,iBAAM2B,SAAN,CAAgB1B,GAAhB,CAC9EwB,IAD8E,CAE9E,EAFF,CAH2D,CAO3D;AACA;;AACA9B,qBAAOiC,GAAP,CAAWL,MAAX,EAT2D,CAW3D;;;AACA,MAAIA,MAAM,CAACM,QAAP,CAAgB,sBAAhB,CAAJ,EAA6C;AAC3CH,IAAAA,OAAO,GACL,sEADF;AAED,GAHD,MAGO,IACLH,MAAM,CAACM,QAAP,CAAgB,iCAAhB,KACAN,MAAM,CAACM,QAAP,CAAgB,wBAAhB,CAFK,EAGL;AACAH,IAAAA,OAAO,GAAI,gFAA+E1B,iBAAM8B,IAAN,CACxF,+CADwF,CAExF,GAFF;AAGD;;AAED,SAAO,KAAIC,oBAAJ,EAAc,8BAA6BL,OAAQ,GAAnD,EAAuD3B,KAAvD,CAAP;AACD;;eAEcd,e", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport chalk from 'chalk';\nimport execa from 'execa';\nimport {Config} from '@react-native-community/cli-types';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\nimport adb from './adb';\nimport tryRunAdbReverse from './tryRunAdbReverse';\nimport tryLaunchAppOnDevice from './tryLaunchAppOnDevice';\nimport tryLaunchEmulator from './tryLaunchEmulator';\nimport {Flags} from '.';\n\nfunction getTaskNames(appName: string, commands: Array<string>): Array<string> {\n  return appName\n    ? commands.map((command) => `${appName}:${command}`)\n    : commands;\n}\n\nfunction toPascalCase(value: string) {\n  return value !== '' ? value[0].toUpperCase() + value.slice(1) : value;\n}\n\ntype AndroidProject = NonNullable<Config['project']['android']>;\n\nasync function runOnAllDevices(\n  args: Flags,\n  cmd: string,\n  packageName: string,\n  adbPath: string,\n  androidProject: AndroidProject,\n) {\n  let devices = adb.getDevices(adbPath);\n  if (devices.length === 0) {\n    logger.info('Launching emulator...');\n    const result = await tryLaunchEmulator(adbPath);\n    if (result.success) {\n      logger.info('Successfully launched emulator.');\n      devices = adb.getDevices(adbPath);\n    } else {\n      logger.error(\n        `Failed to launch emulator. Reason: ${chalk.dim(result.error || '')}.`,\n      );\n      logger.warn(\n        'Please launch an emulator manually or connect a device. Otherwise app may fail to launch.',\n      );\n    }\n  }\n\n  try {\n    const tasks = args.tasks || ['install' + toPascalCase(args.variant)];\n    const gradleArgs = getTaskNames(\n      args.appFolder || androidProject.appName,\n      tasks,\n    );\n\n    if (args.port != null) {\n      gradleArgs.push('-PreactNativeDevServerPort=' + args.port);\n    }\n\n    if (args.activeArchOnly) {\n      const architectures = devices\n        .map((device) => {\n          return adb.getCPU(adbPath, device);\n        })\n        .filter((arch) => arch != null);\n      if (architectures.length > 0) {\n        logger.info(`Detected architectures ${architectures.join(', ')}`);\n        // `reactNativeDebugArchitectures` was renamed to `reactNativeArchitectures` in 0.68.\n        // Can be removed when 0.67 no longer needs to be supported.\n        gradleArgs.push(\n          '-PreactNativeDebugArchitectures=' + architectures.join(','),\n        );\n        gradleArgs.push(\n          '-PreactNativeArchitectures=' + architectures.join(','),\n        );\n      }\n    }\n\n    logger.info('Installing the app...');\n    logger.debug(\n      `Running command \"cd android && ${cmd} ${gradleArgs.join(' ')}\"`,\n    );\n\n    await execa(cmd, gradleArgs, {\n      stdio: ['inherit', 'inherit', 'pipe'],\n      cwd: androidProject.sourceDir,\n    });\n  } catch (error) {\n    throw createInstallError(error);\n  }\n\n  (devices.length > 0 ? devices : [undefined]).forEach(\n    (device: string | void) => {\n      tryRunAdbReverse(args.port, device);\n      tryLaunchAppOnDevice(device, packageName, adbPath, args);\n    },\n  );\n}\n\nfunction createInstallError(error: Error & {stderr: string}) {\n  const stderr = (error.stderr || '').toString();\n  const docs = 'https://reactnative.dev/docs/environment-setup';\n  let message = `Make sure you have the Android development environment set up: ${chalk.underline.dim(\n    docs,\n  )}`;\n\n  // Pass the error message from the command to stdout because we pipe it to\n  // parent process so it's not visible\n  logger.log(stderr);\n\n  // Handle some common failures and make the errors more helpful\n  if (stderr.includes('No connected devices')) {\n    message =\n      'Make sure you have an Android emulator running or a device connected';\n  } else if (\n    stderr.includes('licences have not been accepted') ||\n    stderr.includes('accept the SDK license')\n  ) {\n    message = `Please accept all necessary Android SDK licenses using Android SDK Manager: \"${chalk.bold(\n      '$ANDROID_HOME/tools/bin/sdkmanager --licenses',\n    )}\"`;\n  }\n\n  return new CLIError(`Failed to install the app. ${message}.`, error);\n}\n\nexport default runOnAllDevices;\n"]}