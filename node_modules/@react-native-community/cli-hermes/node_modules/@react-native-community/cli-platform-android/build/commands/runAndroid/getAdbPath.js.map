{"version": 3, "sources": ["../../../src/commands/runAndroid/getAdbPath.ts"], "names": ["getAdbPath", "process", "env", "ANDROID_HOME", "path", "join"], "mappings": ";;;;;;;AAOA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAPA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA,SAASA,UAAT,GAAsB;AACpB,SAAOC,OAAO,CAACC,GAAR,CAAYC,YAAZ,GACHC,gBAAKC,IAAL,CAAUJ,OAAO,CAACC,GAAR,CAAYC,YAAtB,EAAoC,gBAApC,EAAsD,KAAtD,CADG,GAEH,KAFJ;AAGD;;eAEcH,U", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport path from 'path';\n\nfunction getAdbPath() {\n  return process.env.ANDROID_HOME\n    ? path.join(process.env.ANDROID_HOME, 'platform-tools', 'adb')\n    : 'adb';\n}\n\nexport default getAdbPath;\n"]}