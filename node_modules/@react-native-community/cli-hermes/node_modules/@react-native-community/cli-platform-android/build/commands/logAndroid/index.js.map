{"version": 3, "sources": ["../../../src/commands/logAndroid/index.ts"], "names": ["logAndroid", "logger", "info", "emitter", "platform", "priority", "AndroidPriority", "VERBOSE", "filter", "on", "entry", "log", "error", "name", "description", "func"], "mappings": ";;;;;;;AAOA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAOA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAdA;AACA;AACA;AACA;AACA;AACA;AAWA,eAAeA,UAAf,GAA4B;AAC1BC,qBAAOC,IAAP,CAAY,mBAAZ;;AAEA,QAAMC,OAAO,GAAG,0BAAS;AACvBC,IAAAA,QAAQ,EAAE,SADa;AAEvBC,IAAAA,QAAQ,EAAEC,4BAAgBC,OAFH;AAGvBC,IAAAA,MAAM,EAAE,gCAAe,aAAf,EAA8B,eAA9B;AAHe,GAAT,CAAhB;AAMAL,EAAAA,OAAO,CAACM,EAAR,CAAW,OAAX,EAAqBC,KAAD,IAAW;AAC7BT,uBAAOU,GAAP,CAAW,6BAAYD,KAAZ,CAAX;AACD,GAFD;AAIAP,EAAAA,OAAO,CAACM,EAAR,CAAW,OAAX,EAAqBG,KAAD,IAAW;AAC7BX,uBAAOU,GAAP,CAAW,6BAAYC,KAAZ,CAAX;AACD,GAFD;AAGD;;eAEc;AACbC,EAAAA,IAAI,EAAE,aADO;AAEbC,EAAAA,WAAW,EAAE,iBAFA;AAGbC,EAAAA,IAAI,EAAEf;AAHO,C", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport {\n  logkitty,\n  makeTagsFilter,\n  formatEntry,\n  formatError,\n  AndroidPriority,\n} from 'logkitty';\nimport {logger} from '@react-native-community/cli-tools';\n\nasync function logAndroid() {\n  logger.info('Starting logkitty');\n\n  const emitter = logkitty({\n    platform: 'android',\n    priority: AndroidPriority.VERBOSE,\n    filter: makeTagsFilter('ReactNative', 'ReactNativeJS'),\n  });\n\n  emitter.on('entry', (entry) => {\n    logger.log(formatEntry(entry));\n  });\n\n  emitter.on('error', (error) => {\n    logger.log(formatError(error));\n  });\n}\n\nexport default {\n  name: 'log-android',\n  description: 'starts logkitty',\n  func: logAndroid,\n};\n"]}