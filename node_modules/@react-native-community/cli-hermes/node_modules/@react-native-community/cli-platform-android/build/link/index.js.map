{"version": 3, "sources": ["../../src/link/index.ts"], "names": ["getAndroidLinkConfig", "isInstalled", "register", "unregister", "copyAssets", "unlinkAssets"], "mappings": ";;;;;;;;AAQA;;AACA;;AACA;;AACA;;AACA;;;;AAZA;AACA;AACA;AACA;AACA;AACA;AACA;AAQO,SAASA,oBAAT,GAAgC;AACrC,SAAO;AAACC,IAAAA,WAAW,EAAXA,oBAAD;AAAcC,IAAAA,QAAQ,EAARA,6BAAd;AAAwBC,IAAAA,UAAU,EAAVA,+BAAxB;AAAoCC,IAAAA,UAAU,EAAVA,mBAApC;AAAgDC,IAAAA,YAAY,EAAZA;AAAhD,GAAP;AACD;;eAEcL,oB", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport isInstalled from './isInstalled';\nimport register from './registerNativeModule';\nimport unregister from './unregisterNativeModule';\nimport copyAssets from './copyAssets';\nimport unlinkAssets from './unlinkAssets';\n\nexport function getAndroidLinkConfig() {\n  return {isInstalled, register, unregister, copyAssets, unlinkAssets};\n}\n\nexport default getAndroidLinkConfig;\n"]}