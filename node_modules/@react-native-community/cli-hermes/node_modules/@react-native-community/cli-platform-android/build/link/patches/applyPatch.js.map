{"version": 3, "sources": ["../../../src/link/patches/applyPatch.ts"], "names": ["applyPatch", "file", "patch", "logger", "debug", "fs", "writeFileSync", "readFileSync", "replace", "pattern", "match"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,UAAT,CACbC,IADa,EAEbC,KAFa,EAGb;AACA,MAAID,IAAJ,EAAU;AACRE,uBAAOC,KAAP,CAAc,YAAWH,IAAK,EAA9B;AACD;;AAEDI,gBAAGC,aAAH,CACEL,IADF,EAEEI,cACGE,YADH,CACgBN,IADhB,EACsB,MADtB,EAEGO,OAFH,CAEWN,KAAK,CAACO,OAFjB,EAE2BC,KAAD,IAAY,GAAEA,KAAM,GAAER,KAAK,CAACA,KAAM,EAF5D,CAFF;AAMD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport {logger} from '@react-native-community/cli-tools';\n\nexport default function applyPatch(\n  file: string,\n  patch: {patch: string; pattern: string | RegExp},\n) {\n  if (file) {\n    logger.debug(`Patching ${file}`);\n  }\n\n  fs.writeFileSync(\n    file,\n    fs\n      .readFileSync(file, 'utf8')\n      .replace(patch.pattern, (match) => `${match}${patch.patch}`),\n  );\n}\n"]}