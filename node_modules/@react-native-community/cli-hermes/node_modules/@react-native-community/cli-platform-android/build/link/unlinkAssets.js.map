{"version": 3, "sources": ["../../src/link/unlinkAssets.ts"], "names": ["unlinkAssetsAndroid", "files", "project", "assets", "logger", "debug", "assetsPath", "font", "for<PERSON>ach", "file", "filePath", "path", "join", "basename", "fs", "existsSync", "unlinkSync"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;;AAMA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,mBAAT,CACbC,KADa,EAEbC,OAFa,EAGb;AACA,QAAMC,MAAM,GAAG,kCAAiBF,KAAjB,CAAf;;AAEAG,qBAAOC,KAAP,CAAc,gBAAeH,OAAO,CAACI,UAAW,EAAhD;;AACA,GAACH,MAAM,CAACI,IAAP,IAAe,EAAhB,EAAoBC,OAApB,CAA6BC,IAAD,IAAU;AACpC,UAAMC,QAAQ,GAAGC,gBAAKC,IAAL,CACfV,OAAO,CAACI,UADO,EAEf,OAFe,EAGfK,gBAAKE,QAAL,CAAcJ,IAAd,CAHe,CAAjB;;AAKA,QAAIK,cAAGC,UAAH,CAAcL,QAAd,CAAJ,EAA6B;AAC3BN,yBAAOC,KAAP,CAAc,kBAAiBK,QAAS,EAAxC;;AACAI,oBAAGE,UAAH,CAAcN,QAAd;AACD;AACF,GAVD;AAWD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport path from 'path';\nimport {logger, groupFilesByType} from '@react-native-community/cli-tools';\n\n/**\n * Copies each file from an array of assets provided to targetPath directory\n *\n * For now, the only types of files that are handled are:\n * - Fonts (otf, ttf) - copied to targetPath/fonts under original name\n */\nexport default function unlinkAssetsAndroid(\n  files: Array<string>,\n  project: {assetsPath: string},\n) {\n  const assets = groupFilesByType(files);\n\n  logger.debug(`Assets path: ${project.assetsPath}`);\n  (assets.font || []).forEach((file) => {\n    const filePath = path.join(\n      project.assetsPath,\n      'fonts',\n      path.basename(file),\n    );\n    if (fs.existsSync(filePath)) {\n      logger.debug(`Removing asset ${filePath}`);\n      fs.unlinkSync(filePath);\n    }\n  });\n}\n"]}