{"version": 3, "sources": ["../../../src/link/patches/makeImportPatch.ts"], "names": ["makeImportPatch", "packageImportPath", "pattern", "patch"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEe,SAASA,eAAT,CAAyBC,iBAAzB,EAAoD;AACjE,SAAO;AACLC,IAAAA,OAAO,EAAE,6CADJ;AAELC,IAAAA,KAAK,EAAG,KAAIF,iBAAkB;AAFzB,GAAP;AAID", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nexport default function makeImportPatch(packageImportPath: string) {\n  return {\n    pattern: 'import com.facebook.react.ReactApplication;',\n    patch: `\\n${packageImportPath}`,\n  };\n}\n"]}