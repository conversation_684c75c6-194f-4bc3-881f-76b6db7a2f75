{"version": 3, "sources": ["../../../src/link/patches/makeBuildPatch.ts"], "names": ["depConfigs", "makeBuildPatch", "name", "buildGradlePath", "normalizedProjectName", "installPattern", "RegExp", "buildDepRegExp", "pattern", "patch", "makePatchString", "defaultPatchString", "buildGradle", "fs", "readFileSync", "config", "depPattern", "test", "configs", "orConfigs", "join"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA,MAAMA,UAAU,GAAG,CAAC,SAAD,EAAY,KAAZ,EAAmB,gBAAnB,CAAnB;;AAEe,SAASC,cAAT,CAAwBC,IAAxB,EAAsCC,eAAtC,EAAgE;AAC7E,QAAMC,qBAAqB,GAAG,mCAAqBF,IAArB,CAA9B;AACA,QAAMG,cAAc,GAAG,IAAIC,MAAJ,CACrBC,cAAc,CAACH,qBAAD,EAAwB,GAAGJ,UAA3B,CADO,CAAvB;AAIA,SAAO;AACLK,IAAAA,cADK;AAELG,IAAAA,OAAO,EAAE,+BAFJ;AAGLC,IAAAA,KAAK,EAAEC,eAAe,CAACN,qBAAD,EAAwBD,eAAxB;AAHjB,GAAP;AAKD;;AAED,SAASO,eAAT,CACEN,qBADF,EAEED,eAFF,EAGE;AACA,QAAMQ,kBAAkB,GAAI,gCAA+BP,qBAAsB,MAAjF;;AACA,MAAI,CAACD,eAAL,EAAsB;AACpB,WAAOQ,kBAAP;AACD;;AAED,QAAMC,WAAW,GAAGC,cAAGC,YAAH,CAAgBX,eAAhB,EAAiC,MAAjC,CAApB;;AAEA,OAAK,MAAMY,MAAX,IAAqBf,UAArB,EAAiC;AAC/B,UAAMgB,UAAU,GAAG,IAAIV,MAAJ,CACjBC,cAAc,CAACH,qBAAD,EAAwBW,MAAxB,CADG,CAAnB;;AAGA,QAAIC,UAAU,CAACC,IAAX,CAAgBL,WAAhB,CAAJ,EAAkC;AAChC,aAAQ,OAAMG,MAAO,cAAaX,qBAAsB,MAAxD;AACD;AACF;;AAED,SAAOO,kBAAP;AACD;;AAED,SAASJ,cAAT,CACEH,qBADF,EAEE,GAAGc,OAFL,EAGE;AACA,QAAMC,SAAS,GAAGD,OAAO,CAACE,IAAR,CAAa,GAAb,CAAlB;AACA,SAAQ,IAAGD,SAAU,mCAAkCf,qBAAsB,SAA7E;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport normalizeProjectName from './normalizeProjectName';\n\nconst depConfigs = ['compile', 'api', 'implementation'];\n\nexport default function makeBuildPatch(name: string, buildGradlePath?: string) {\n  const normalizedProjectName = normalizeProjectName(name);\n  const installPattern = new RegExp(\n    buildDepRegExp(normalizedProjectName, ...depConfigs),\n  );\n\n  return {\n    installPattern,\n    pattern: /[^ \\t]dependencies {(\\r\\n|\\n)/,\n    patch: makePatchString(normalizedProjectName, buildGradlePath),\n  };\n}\n\nfunction makePatchString(\n  normalizedProjectName: string,\n  buildGradlePath?: string,\n) {\n  const defaultPatchString = `    implementation project(':${normalizedProjectName}')\\n`;\n  if (!buildGradlePath) {\n    return defaultPatchString;\n  }\n\n  const buildGradle = fs.readFileSync(buildGradlePath, 'utf8');\n\n  for (const config of depConfigs) {\n    const depPattern = new RegExp(\n      buildDepRegExp(normalizedProjectName, config),\n    );\n    if (depPattern.test(buildGradle)) {\n      return `    ${config} project(':${normalizedProjectName}')\\n`;\n    }\n  }\n\n  return defaultPatchString;\n}\n\nfunction buildDepRegExp(\n  normalizedProjectName: string,\n  ...configs: Array<string>\n) {\n  const orConfigs = configs.join('|');\n  return `(${orConfigs})\\\\w*\\\\s*\\\\(*project\\\\s*\\\\(['\"]:${normalizedProjectName}['\"]\\\\)`;\n}\n"]}