/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { AndroidProjectConfig, AndroidDependencyConfig } from '@react-native-community/cli-types';
export default function unregisterNativeAndroidModule(name: string, androidConfig: AndroidDependencyConfig, projectConfig: AndroidProjectConfig): void;
//# sourceMappingURL=unregisterNativeModule.d.ts.map