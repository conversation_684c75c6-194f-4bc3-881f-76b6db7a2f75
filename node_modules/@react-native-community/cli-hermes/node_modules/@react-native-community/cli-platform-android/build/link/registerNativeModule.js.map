{"version": 3, "sources": ["../../src/link/registerNativeModule.ts"], "names": ["registerNativeAndroidModule", "name", "androidConfig", "params", "projectConfig", "buildPatch", "<PERSON>GradlePath", "buildGradlePath", "stringsPath", "mainFilePath", "packageInstance", "packageImportPath"], "mappings": ";;;;;;;AAQA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;AAbA;AACA;AACA;AACA;AACA;AACA;AACA;AAce,SAASA,2BAAT,CACbC,IADa,EAEbC,aAFa,EAGbC,MAHa,EAIbC,aAJa,EAKb;AACA,QAAMC,UAAU,GAAG,6BAAeJ,IAAf,CAAnB;AAEA,2BACEG,aAAa,CAACE,kBADhB,EAEE,gCAAkBL,IAAlB,EAAwBC,aAAxB,EAAuCE,aAAvC,CAFF;AAKA,2BAAWA,aAAa,CAACG,eAAzB,EAA0CF,UAA1C;AACA,2BAAWD,aAAa,CAACI,WAAzB,EAAsC,+BAAiBL,MAAjB,EAAyBF,IAAzB,CAAtC;AAEA,2BACEG,aAAa,CAACK,YADhB,EAEE,+BAAiBP,aAAa,CAACQ,eAA/B,EAAgDP,MAAhD,EAAwDF,IAAxD,CAFF;AAKA,2BACEG,aAAa,CAACK,YADhB,EAEE,8BAAgBP,aAAa,CAACS,iBAA9B,CAFF;AAID", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport applyPatch from './patches/applyPatch';\nimport makeStringsPatch from './patches/makeStringsPatch';\nimport makeSettingsPatch from './patches/makeSettingsPatch';\nimport makeBuildPatch from './patches/makeBuildPatch';\nimport makeImportPatch from './patches/makeImportPatch';\nimport makePackagePatch from './patches/makePackagePatch';\nimport {\n  AndroidProjectConfig,\n  AndroidDependencyConfig,\n  AndroidProjectParams,\n} from '@react-native-community/cli-types';\n\nexport default function registerNativeAndroidModule(\n  name: string,\n  androidConfig: AndroidDependencyConfig,\n  params: AndroidProjectParams,\n  projectConfig: AndroidProjectConfig,\n) {\n  const buildPatch = makeBuildPatch(name);\n\n  applyPatch(\n    projectConfig.settingsGradlePath,\n    makeSettingsPatch(name, androidConfig, projectConfig),\n  );\n\n  applyPatch(projectConfig.buildGradlePath, buildPatch);\n  applyPatch(projectConfig.stringsPath, makeStringsPatch(params, name));\n\n  applyPatch(\n    projectConfig.mainFilePath,\n    makePackagePatch(androidConfig.packageInstance, params, name),\n  );\n\n  applyPatch(\n    projectConfig.mainFilePath,\n    makeImportPatch(androidConfig.packageImportPath),\n  );\n}\n"]}