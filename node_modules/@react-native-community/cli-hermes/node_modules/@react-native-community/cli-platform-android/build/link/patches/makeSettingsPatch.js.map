{"version": 3, "sources": ["../../../src/link/patches/makeSettingsPatch.ts"], "names": ["makeSettingsPatch", "name", "androidConfig", "projectConfig", "projectDir", "path", "relative", "dirname", "<PERSON>GradlePath", "sourceDir", "normalizedProjectName", "pattern", "patch"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;AAMe,SAASA,iBAAT,CACbC,IADa,EAEbC,aAFa,EAGbC,aAHa,EAIb;AACA;AACA,QAAMC,UAAU,GAAG,sBACjBC,gBAAKC,QAAL,CACED,gBAAKE,OAAL,CAAaJ,aAAa,CAACK,kBAA3B,CADF,EAEEN,aAAa,CAACO,SAFhB,CADiB,CAAnB;AAMA,QAAMC,qBAAqB,GAAG,mCAAqBT,IAArB,CAA9B;AAEA,SAAO;AACLU,IAAAA,OAAO,EAAE,IADJ;AAELC,IAAAA,KAAK,EACF,aAAYF,qBAAsB,KAAnC,GACC,aAAYA,qBAAsB,kBADnC,GAEC,qCAAoCN,UAAW;AAL7C,GAAP;AAOD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport slash from 'slash';\nimport normalizeProjectName from './normalizeProjectName';\n\nexport default function makeSettingsPatch(\n  name: string,\n  androidConfig: {sourceDir: string},\n  projectConfig: {settingsGradlePath: string},\n) {\n  // <PERSON>rad<PERSON> expects paths to be posix even on Windows\n  const projectDir = slash(\n    path.relative(\n      path.dirname(projectConfig.settingsGradlePath),\n      androidConfig.sourceDir,\n    ),\n  );\n  const normalizedProjectName = normalizeProjectName(name);\n\n  return {\n    pattern: '\\n',\n    patch:\n      `include ':${normalizedProjectName}'\\n` +\n      `project(':${normalizedProjectName}').projectDir = ` +\n      `new File(rootProject.projectDir, '${projectDir}')\\n`,\n  };\n}\n"]}