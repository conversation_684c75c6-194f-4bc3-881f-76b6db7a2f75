{"version": 3, "sources": ["../../src/link/unregisterNativeModule.ts"], "names": ["unregisterNativeAndroidModule", "name", "androidConfig", "projectConfig", "buildPatch", "buildGradlePath", "strings", "fs", "readFileSync", "stringsPath", "params", "replace", "_", "param", "value", "slice", "length", "<PERSON>GradlePath", "mainFilePath", "packageInstance", "packageImportPath"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;;;AAhBA;AACA;AACA;AACA;AACA;AACA;AACA;AAgBe,SAASA,6BAAT,CACbC,IADa,EAEbC,aAFa,EAGbC,aAHa,EAIb;AACA,QAAMC,UAAU,GAAG,6BAAeH,IAAf,EAAqBE,aAAa,CAACE,eAAnC,CAAnB;;AACA,QAAMC,OAAO,GAAGC,cAAGC,YAAH,CAAgBL,aAAa,CAACM,WAA9B,EAA2C,MAA3C,CAAhB;;AACA,QAAMC,MAAM,GAAG,EAAf;AAEAJ,EAAAA,OAAO,CAACK,OAAR,CACE,yCADF,EAEE;AACA,GAACC,CAAD,EAAIC,KAAJ,EAAWC,KAAX,KAAqB;AACnB;AACAJ,IAAAA,MAAM,CAACG,KAAK,CAACE,KAAN,CAAY,yBAAYd,IAAZ,EAAkBe,MAAlB,GAA2B,CAAvC,CAAD,CAAN,GAAoDF,KAApD;AACD,GANH;AASA,4BACEX,aAAa,CAACc,kBADhB,EAEE,gCAAkBhB,IAAlB,EAAwBC,aAAxB,EAAuCC,aAAvC,CAFF;AAKA,4BAAYA,aAAa,CAACE,eAA1B,EAA2CD,UAA3C;AACA,4BAAYD,aAAa,CAACM,WAA1B,EAAuC,+BAAiBC,MAAjB,EAAyBT,IAAzB,CAAvC;AAEA,4BACEE,aAAa,CAACe,YADhB,EAEE,+BAAiBhB,aAAa,CAACiB,eAA/B,EAAgDT,MAAhD,EAAwDT,IAAxD,CAFF;AAKA,4BACEE,aAAa,CAACe,YADhB,EAEE,8BAAgBhB,aAAa,CAACkB,iBAA9B,CAFF;AAID", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport fs from 'fs';\nimport {camelCase as toCamelCase} from 'lodash';\n\nimport revokePatch from './patches/revokePatch';\nimport makeSettingsPatch from './patches/makeSettingsPatch';\nimport makeBuildPatch from './patches/makeBuildPatch';\nimport makeStringsPatch from './patches/makeStringsPatch';\nimport makeImportPatch from './patches/makeImportPatch';\nimport makePackagePatch from './patches/makePackagePatch';\nimport {\n  AndroidProjectConfig,\n  AndroidDependencyConfig,\n} from '@react-native-community/cli-types';\n\nexport default function unregisterNativeAndroidModule(\n  name: string,\n  androidConfig: AndroidDependencyConfig,\n  projectConfig: AndroidProjectConfig,\n) {\n  const buildPatch = makeBuildPatch(name, projectConfig.buildGradlePath);\n  const strings = fs.readFileSync(projectConfig.stringsPath, 'utf8');\n  const params = {};\n\n  strings.replace(\n    /moduleConfig=\"true\" name=\"(\\w+)\">(.*)</g,\n    // @ts-ignore\n    (_, param, value) => {\n      // @ts-ignore\n      params[param.slice(toCamelCase(name).length + 1)] = value;\n    },\n  );\n\n  revokePatch(\n    projectConfig.settingsGradlePath,\n    makeSettingsPatch(name, androidConfig, projectConfig),\n  );\n\n  revokePatch(projectConfig.buildGradlePath, buildPatch);\n  revokePatch(projectConfig.stringsPath, makeStringsPatch(params, name));\n\n  revokePatch(\n    projectConfig.mainFilePath,\n    makePackagePatch(androidConfig.packageInstance, params, name),\n  );\n\n  revokePatch(\n    projectConfig.mainFilePath,\n    makeImportPatch(androidConfig.packageImportPath),\n  );\n}\n"]}