{"version": 3, "sources": ["../../../src/link/patches/makePackagePatch.ts"], "names": ["makePackagePatch", "packageInstance", "params", "prefix", "processedInstance", "pattern", "patch"], "mappings": ";;;;;;;AAQA;;;;AARA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,gBAAT,CACbC,eADa,EAEbC,MAFa,EAGbC,MAHa,EAIb;AACA,QAAMC,iBAAiB,GAAG,0BAAYH,eAAZ,EAA6BC,MAA7B,EAAqCC,MAArC,CAA1B;AAEA,SAAO;AACLE,IAAAA,OAAO,EAAE,wBADJ;AAELC,IAAAA,KAAK,EAAG,kBAAiBF,iBAAkB;AAFtC,GAAP;AAID", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport applyParams from './applyParams';\nimport {AndroidProjectParams} from '@react-native-community/cli-types';\n\nexport default function makePackagePatch(\n  packageInstance: string,\n  params: AndroidProjectParams,\n  prefix: string,\n) {\n  const processedInstance = applyParams(packageInstance, params, prefix);\n\n  return {\n    pattern: 'new MainReactPackage()',\n    patch: `,\\n            ${processedInstance}`,\n  };\n}\n"]}