/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { AndroidProjectConfig, AndroidDependencyConfig, AndroidProjectParams } from '@react-native-community/cli-types';
export default function registerNativeAndroidModule(name: string, androidConfig: AndroidDependencyConfig, params: AndroidProjectParams, projectConfig: AndroidProjectConfig): void;
//# sourceMappingURL=registerNativeModule.d.ts.map