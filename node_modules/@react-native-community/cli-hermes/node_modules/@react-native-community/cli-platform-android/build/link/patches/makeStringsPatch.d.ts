/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { AndroidProjectParams } from '@react-native-community/cli-types';
export default function makeStringsPatch(params: AndroidProjectParams, prefix: string): {
    pattern: string;
    patch: string;
};
//# sourceMappingURL=makeStringsPatch.d.ts.map