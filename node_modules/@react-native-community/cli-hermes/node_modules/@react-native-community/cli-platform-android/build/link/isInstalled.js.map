{"version": 3, "sources": ["../../src/link/isInstalled.ts"], "names": ["isInstalled", "config", "name", "buildGradle", "fs", "existsSync", "buildGradlePath", "CLIError", "readFileSync", "installPattern", "test"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;;;AAVA;AACA;AACA;AACA;AACA;AACA;AACA;AAMe,SAASA,WAAT,CACbC,MADa,EAEbC,IAFa,EAGb;AACA,MAAIC,WAAJ;;AAEA,MAAI,CAACC,cAAGC,UAAH,CAAcJ,MAAM,CAACK,eAArB,CAAL,EAA4C;AAC1C;AACA,QAAI,CAACF,cAAGC,UAAH,CAAcJ,MAAM,CAACK,eAAP,GAAyB,MAAvC,CAAL,EAAqD;AACnD,YAAM,KAAIC,oBAAJ,EACJ,0CAA0CN,MAAM,CAACK,eAD7C,CAAN;AAGD,KAJD,MAIO;AACLH,MAAAA,WAAW,GAAGC,cAAGI,YAAH,CAAgBP,MAAM,CAACK,eAAP,GAAyB,MAAzC,EAAiD,MAAjD,CAAd;AACD;AACF,GATD,MASO;AACLH,IAAAA,WAAW,GAAGC,cAAGI,YAAH,CAAgBP,MAAM,CAACK,eAAvB,EAAwC,MAAxC,CAAd;AACD;;AAED,SAAO,6BAAeJ,IAAf,EAAqBO,cAArB,CAAoCC,IAApC,CAAyCP,WAAzC,CAAP;AACD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {CLIError} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport makeBuildPatch from './patches/makeBuildPatch';\n\nexport default function isInstalled(\n  config: {buildGradlePath: string},\n  name: string,\n) {\n  let buildGradle: string;\n\n  if (!fs.existsSync(config.buildGradlePath)) {\n    // Handle default build.gradle path for Gradle Kotlin DSL\n    if (!fs.existsSync(config.buildGradlePath + '.kts')) {\n      throw new CLIError(\n        'Cannot resolve build.gradle file at: ' + config.buildGradlePath,\n      );\n    } else {\n      buildGradle = fs.readFileSync(config.buildGradlePath + '.kts', 'utf8');\n    }\n  } else {\n    buildGradle = fs.readFileSync(config.buildGradlePath, 'utf8');\n  }\n\n  return makeBuildPatch(name).installPattern.test(buildGradle);\n}\n"]}