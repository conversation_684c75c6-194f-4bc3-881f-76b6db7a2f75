{"version": 3, "sources": ["../../../src/link/patches/applyParams.ts"], "names": ["applyParams", "str", "params", "prefix", "replace", "_pattern", "param", "name"], "mappings": ";;;;;;;AAQA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AARA;AACA;AACA;AACA;AACA;AACA;AACA;AAKe,SAASA,WAAT,CACbC,GADa,EAEbC,MAFa,EAGbC,MAHa,EAIb;AACA,SAAOF,GAAG,CAACG,OAAJ,CAAY,cAAZ,EAA4B,CAACC,QAAD,EAAmBC,KAAnB,KAAqC;AACtE,UAAMC,IAAI,GAAI,GAAE,yBAAYJ,MAAZ,CAAoB,IAAGG,KAAM,EAA7C,CADsE,CAGtE;;AACA,WAAOJ,MAAM,CAACI,KAAD,CAAN,GACF,qCAAoCC,IAAK,GADvC,GAEH,MAFJ;AAGD,GAPM,CAAP;AAQD", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {camelCase as toCamelCase} from 'lodash';\nimport {AndroidProjectParams} from '@react-native-community/cli-types';\n\nexport default function applyParams(\n  str: string,\n  params: AndroidProjectParams,\n  prefix: string,\n) {\n  return str.replace(/\\$\\{(\\w+)\\}/g, (_pattern: string, param: string) => {\n    const name = `${toCamelCase(prefix)}_${param}`;\n\n    // @ts-ignore\n    return params[param]\n      ? `getResources().getString(R.string.${name})`\n      : 'null';\n  });\n}\n"]}