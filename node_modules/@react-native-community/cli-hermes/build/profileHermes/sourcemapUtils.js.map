{"version": 3, "sources": ["../../src/profileHermes/sourcemapUtils.ts"], "names": ["getTempFilePath", "filename", "path", "join", "os", "tmpdir", "writeJsonSync", "targetPath", "data", "json", "JSON", "stringify", "e", "CLIError", "fs", "writeFileSync", "getSourcemapFromServer", "port", "logger", "debug", "DEBUG_SERVER_PORT", "IP_ADDRESS", "ip", "address", "PLATFORM", "requestURL", "undefined", "generateSourcemap", "sourceMapPath", "sourceMapResult", "error", "findSourcemap", "ctx", "intermediateBuildPath", "root", "generatedBuildPath", "existsSync"], "mappings": ";;;;;;;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAGA,SAASA,eAAT,CAAyBC,QAAzB,EAA2C;AACzC,SAAOC,gBAAKC,IAAL,CAAUC,cAAGC,MAAH,EAAV,EAAuBJ,QAAvB,CAAP;AACD;;AAED,SAASK,aAAT,CAAuBC,UAAvB,EAA2CC,IAA3C,EAAsD;AACpD,MAAIC,IAAJ;;AACA,MAAI;AACFA,IAAAA,IAAI,GAAGC,IAAI,CAACC,SAAL,CAAeH,IAAf,CAAP;AACD,GAFD,CAEE,OAAOI,CAAP,EAAU;AACV,UAAM,KAAIC,oBAAJ,EACH,sDAAqDN,UAAW,EAD7D,EAEJK,CAFI,CAAN;AAID;;AAED,MAAI;AACFE,kBAAGC,aAAH,CAAiBR,UAAjB,EAA6BE,IAA7B,EAAmC,OAAnC;AACD,GAFD,CAEE,OAAOG,CAAP,EAAU;AACV,UAAM,KAAIC,oBAAJ,EAAc,2BAA0BN,UAAW,EAAnD,EAAsDK,CAAtD,CAAN;AACD;AACF;;AAED,eAAeI,sBAAf,CACEC,IADF,EAEkC;AAChCC,qBAAOC,KAAP,CAAa,gDAAb;;AACA,QAAMC,iBAAiB,GAAGH,IAAI,IAAI,MAAlC;;AACA,QAAMI,UAAU,GAAGC,cAAGC,OAAH,EAAnB;;AACA,QAAMC,QAAQ,GAAG,SAAjB;AAEA,QAAMC,UAAU,GAAI,UAASJ,UAAW,IAAGD,iBAAkB,uBAAsBI,QAAS,WAA5F;;AACA,MAAI;AACF,UAAM;AAAChB,MAAAA;AAAD,QAAS,MAAM,uBAAMiB,UAAN,CAArB;AACA,WAAOjB,IAAP;AACD,GAHD,CAGE,OAAOI,CAAP,EAAU;AACVM,uBAAOC,KAAP,CAAc,oCAAmCM,UAAW,GAA5D;;AACA,WAAOC,SAAP;AACD;AACF;AAED;AACA;AACA;;;AACO,eAAeC,iBAAf,CACLV,IADK,EAEwB;AAC7B;AACA,QAAMW,aAAa,GAAG5B,eAAe,CAAC,WAAD,CAArC;AACA,QAAM6B,eAAe,GAAG,MAAMb,sBAAsB,CAACC,IAAD,CAApD;;AAEA,MAAIY,eAAJ,EAAqB;AACnBX,uBAAOC,KAAP,CAAa,8CAAb;;AACAb,IAAAA,aAAa,CAACsB,aAAD,EAAgBC,eAAhB,CAAb;;AACAX,uBAAOC,KAAP,CACG,yDAAwDS,aAAc,EADzE;;AAGA,WAAOA,aAAP;AACD,GAPD,MAOO;AACLV,uBAAOY,KAAP,CAAa,sDAAb;;AACA,WAAOJ,SAAP;AACD;AACF;AAED;AACA;AACA;AACA;;;AACO,eAAeK,aAAf,CACLC,GADK,EAELf,IAFK,EAGwB;AAC7B,QAAMgB,qBAAqB,GAAG/B,gBAAKC,IAAL,CAC5B6B,GAAG,CAACE,IADwB,EAE5B,SAF4B,EAG5B,KAH4B,EAI5B,OAJ4B,EAK5B,eAL4B,EAM5B,YAN4B,EAO5B,OAP4B,EAQ5B,OAR4B,EAS5B,mCAT4B,CAA9B;;AAYA,QAAMC,kBAAkB,GAAGjC,gBAAKC,IAAL,CACzB6B,GAAG,CAACE,IADqB,EAEzB,SAFyB,EAGzB,KAHyB,EAIzB,OAJyB,EAKzB,WALyB,EAMzB,YANyB,EAOzB,OAPyB,EAQzB,OARyB,EASzB,0BATyB,CAA3B;;AAYA,MAAIpB,cAAGsB,UAAH,CAAcD,kBAAd,CAAJ,EAAuC;AACrCjB,uBAAOC,KAAP,CAAc,+BAA8BQ,iBAAkB,EAA9D;;AACA,WAAOQ,kBAAP;AACD,GAHD,MAGO,IAAIrB,cAAGsB,UAAH,CAAcH,qBAAd,CAAJ,EAA0C;AAC/Cf,uBAAOC,KAAP,CAAc,+BAA8Bc,qBAAsB,EAAlE;;AACA,WAAOA,qBAAP;AACD,GAHM,MAGA;AACL,WAAON,iBAAiB,CAACV,IAAD,CAAxB;AACD;AACF", "sourcesContent": ["import {Config} from '@react-native-community/cli-types';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport path from 'path';\nimport os from 'os';\nimport {SourceMap} from 'hermes-profile-transformer';\nimport ip from 'ip';\nimport {fetch} from '@react-native-community/cli-tools';\n\nfunction getTempFilePath(filename: string) {\n  return path.join(os.tmpdir(), filename);\n}\n\nfunction writeJsonSync(targetPath: string, data: any) {\n  let json;\n  try {\n    json = JSON.stringify(data);\n  } catch (e) {\n    throw new CLIError(\n      `Failed to serialize data to json before writing to ${targetPath}`,\n      e,\n    );\n  }\n\n  try {\n    fs.writeFileSync(targetPath, json, 'utf-8');\n  } catch (e) {\n    throw new CLIError(`Failed to write json to ${targetPath}`, e);\n  }\n}\n\nasync function getSourcemapFromServer(\n  port?: string,\n): Promise<SourceMap | undefined> {\n  logger.debug('Getting source maps from Metro packager server');\n  const DEBUG_SERVER_PORT = port || '8081';\n  const IP_ADDRESS = ip.address();\n  const PLATFORM = 'android';\n\n  const requestURL = `http://${IP_ADDRESS}:${DEBUG_SERVER_PORT}/index.map?platform=${PLATFORM}&dev=true`;\n  try {\n    const {data} = await fetch(requestURL);\n    return data as SourceMap;\n  } catch (e) {\n    logger.debug(`Failed to fetch source map from \"${requestURL}\"`);\n    return undefined;\n  }\n}\n\n/**\n * Generate a sourcemap by fetching it from a running metro server\n */\nexport async function generateSourcemap(\n  port?: string,\n): Promise<string | undefined> {\n  // Fetch the source map to a temp directory\n  const sourceMapPath = getTempFilePath('index.map');\n  const sourceMapResult = await getSourcemapFromServer(port);\n\n  if (sourceMapResult) {\n    logger.debug('Using source maps from Metro packager server');\n    writeJsonSync(sourceMapPath, sourceMapResult);\n    logger.debug(\n      `Successfully obtained the source map and stored it in ${sourceMapPath}`,\n    );\n    return sourceMapPath;\n  } else {\n    logger.error('Cannot obtain source maps from Metro packager server');\n    return undefined;\n  }\n}\n\n/**\n *\n * @param ctx\n */\nexport async function findSourcemap(\n  ctx: Config,\n  port?: string,\n): Promise<string | undefined> {\n  const intermediateBuildPath = path.join(\n    ctx.root,\n    'android',\n    'app',\n    'build',\n    'intermediates',\n    'sourcemaps',\n    'react',\n    'debug',\n    'index.android.bundle.packager.map',\n  );\n\n  const generatedBuildPath = path.join(\n    ctx.root,\n    'android',\n    'app',\n    'build',\n    'generated',\n    'sourcemaps',\n    'react',\n    'debug',\n    'index.android.bundle.map',\n  );\n\n  if (fs.existsSync(generatedBuildPath)) {\n    logger.debug(`Getting the source map from ${generateSourcemap}`);\n    return generatedBuildPath;\n  } else if (fs.existsSync(intermediateBuildPath)) {\n    logger.debug(`Getting the source map from ${intermediateBuildPath}`);\n    return intermediateBuildPath;\n  } else {\n    return generateSourcemap(port);\n  }\n}\n"]}