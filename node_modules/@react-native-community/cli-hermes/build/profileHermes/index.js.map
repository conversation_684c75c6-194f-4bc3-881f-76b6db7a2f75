{"version": 3, "sources": ["../../src/profileHermes/index.ts"], "names": ["profileHermes", "dst<PERSON><PERSON>", "ctx", "options", "logger", "info", "filename", "sourcemapPath", "raw", "generateSourcemap", "port", "err", "name", "description", "func", "default", "process", "env", "RCT_METRO_PORT", "examples", "desc", "cmd"], "mappings": ";;;;;;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAEA;;AAUA,eAAeA,aAAf,CACE,CAACC,OAAD,CADF,EAEEC,GAFF,EAGEC,OAHF,EAIE;AACA,MAAI;AACFC,uBAAOC,IAAP,CACE,oEADF;;AAGA,QAAI,CAACF,OAAO,CAACG,QAAb,EAAuB;AACrBF,yBAAOC,IAAP,CAAY,8CAAZ;AACD;;AACD,UAAM,sCACJH,GADI,EAEJD,OAFI,EAGJE,OAAO,CAACG,QAHJ,EAIJH,OAAO,CAACI,aAJJ,EAKJJ,OAAO,CAACK,GALJ,EAMJL,OAAO,CAACM,iBANJ,EAOJN,OAAO,CAACO,IAPJ,CAAN;AASD,GAhBD,CAgBE,OAAOC,GAAP,EAAY;AACZ,UAAMA,GAAN;AACD;AACF;;eAEc;AACbC,EAAAA,IAAI,EAAE,iCADO;AAEbC,EAAAA,WAAW,EACT,2IAHW;AAIbC,EAAAA,IAAI,EAAEd,aAJO;AAKbG,EAAAA,OAAO,EAAE,CACP;AACES,IAAAA,IAAI,EAAE,qBADR;AAEEC,IAAAA,WAAW,EACT;AAHJ,GADO,EAMP;AACED,IAAAA,IAAI,EAAE,OADR;AAEEC,IAAAA,WAAW,EACT;AAHJ,GANO,EAWP;AACED,IAAAA,IAAI,EAAE,2BADR;AAEEC,IAAAA,WAAW,EACT;AAHJ,GAXO,EAgBP;AACED,IAAAA,IAAI,EAAE,sBADR;AAEEC,IAAAA,WAAW,EAAE;AAFf,GAhBO,EAoBP;AACED,IAAAA,IAAI,EAAE,iBADR;AAEEG,IAAAA,OAAO,EAAG,GAAEC,OAAO,CAACC,GAAR,CAAYC,cAAZ,IAA8B,IAAK;AAFjD,GApBO,CALI;AA8BbC,EAAAA,QAAQ,EAAE,CACR;AACEC,IAAAA,IAAI,EACF,8FAFJ;AAGEC,IAAAA,GAAG,EAAE;AAHP,GADQ;AA9BG,C", "sourcesContent": ["import {logger, CLIError} from '@react-native-community/cli-tools';\nimport {Config} from '@react-native-community/cli-types';\nimport {downloadProfile} from './downloadProfile';\n\ntype Options = {\n  filename?: string;\n  raw?: boolean;\n  sourcemapPath?: string;\n  generateSourcemap?: boolean;\n  port: string;\n};\n\nasync function profileHermes(\n  [dstPath]: Array<string>,\n  ctx: Config,\n  options: Options,\n) {\n  try {\n    logger.info(\n      'Downloading a Hermes Sampling Profiler from your Android device...',\n    );\n    if (!options.filename) {\n      logger.info('No filename is provided, pulling latest file');\n    }\n    await downloadProfile(\n      ctx,\n      dstPath,\n      options.filename,\n      options.sourcemapPath,\n      options.raw,\n      options.generateSourcemap,\n      options.port,\n    );\n  } catch (err) {\n    throw err as CLIError;\n  }\n}\n\nexport default {\n  name: 'profile-hermes [destinationDir]',\n  description:\n    'Pull and convert a Hermes tracing profile to Chrome tracing profile, then store it in the directory <destinationDir> of the local machine',\n  func: profileHermes,\n  options: [\n    {\n      name: '--filename <string>',\n      description:\n        'File name of the profile to be downloaded, eg. sampling-profiler-trace8593107139682635366.cpuprofile',\n    },\n    {\n      name: '--raw',\n      description:\n        'Pulls the original Hermes tracing profile without any transformation',\n    },\n    {\n      name: '--sourcemap-path <string>',\n      description:\n        'The local path to your source map file, eg. /tmp/sourcemap.json',\n    },\n    {\n      name: '--generate-sourcemap',\n      description: 'Generates the JS bundle and source map',\n    },\n    {\n      name: '--port <number>',\n      default: `${process.env.RCT_METRO_PORT || 8081}`,\n    },\n  ],\n  examples: [\n    {\n      desc:\n        'Download the Hermes Sampling Profiler to the directory <destinationDir> on the local machine',\n      cmd: 'profile-hermes /tmp',\n    },\n  ],\n};\n"]}