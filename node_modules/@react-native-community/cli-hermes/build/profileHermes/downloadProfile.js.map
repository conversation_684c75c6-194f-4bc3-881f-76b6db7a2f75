{"version": 3, "sources": ["../../src/profileHermes/downloadProfile.ts"], "names": ["getLatestFile", "packageName", "file", "toString", "trim", "e", "Error", "execSyncWithLog", "command", "logger", "debug", "downloadProfile", "ctx", "dst<PERSON><PERSON>", "filename", "sourcemapPath", "raw", "shouldGenerateSourcemap", "port", "androidProject", "CLIError", "info", "root", "success", "osTmpDir", "os", "tmpdir", "tempFile<PERSON>ath", "path", "join", "warn", "events", "transformedFilePath", "basename", "fs", "writeFileSync", "JSON", "stringify", "undefined"], "mappings": ";;;;;;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;AACA;;AACA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAAA;;;;AAIA;AACA;AACA;AACA;AACA,SAASA,aAAT,CAAuBC,WAAvB,EAAoD;AAClD,MAAI;AACF,UAAMC,IAAI,GAAG,+BAAU,oBAAmBD,WAAY;AAC1D,SADiB,CAAb;AAEA,WAAOC,IAAI,CAACC,QAAL,GAAgBC,IAAhB,EAAP;AACD,GAJD,CAIE,OAAOC,CAAP,EAAU;AACV,UAAM,IAAIC,KAAJ,CAAUD,CAAV,CAAN;AACD;AACF;;AAED,SAASE,eAAT,CAAyBC,OAAzB,EAA0C;AACxCC,qBAAOC,KAAP,CAAc,GAAEF,OAAQ,EAAxB;;AACA,SAAO,+BAASA,OAAT,CAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACO,eAAeG,eAAf,CACLC,GADK,EAELC,OAFK,EAGLC,QAHK,EAILC,aAJK,EAKLC,GALK,EAMLC,uBANK,EAOLC,IAPK,EAQL;AACA,MAAI;AACF,UAAMC,cAAc,GAAG,6CAAkBP,GAAlB,CAAvB;AACA,UAAMX,WAAW,GAAG,0CAAekB,cAAf,CAApB,CAFE,CAIF;;AACA,UAAMjB,IAAI,GAAGY,QAAQ,IAAId,aAAa,CAACC,WAAD,CAAtC;;AACA,QAAI,CAACC,IAAL,EAAW;AACT,YAAM,KAAIkB,oBAAJ,EACJ,6FADI,CAAN;AAGD;;AAEDX,uBAAOY,IAAP,CAAa,sBAAqBnB,IAAK,EAAvC,EAZE,CAcF;;;AACAW,IAAAA,OAAO,GAAGA,OAAO,IAAID,GAAG,CAACU,IAAzB;;AAEAb,uBAAOC,KAAP,CAAa,yCAAb,EAjBE,CAmBF;;;AACA,QAAIM,GAAJ,EAAS;AACPT,MAAAA,eAAe,CACZ,oBAAmBN,WAAY,cAAaC,IAAK,MAAKW,OAAQ,IAAGX,IAAK,EAD1D,CAAf;;AAGAO,yBAAOc,OAAP,CAAgB,mCAAkCV,OAAQ,IAAGX,IAAK,EAAlE;AACD,KALD,CAOA;AAPA,SAQK;AACH,cAAMsB,QAAQ,GAAGC,cAAGC,MAAH,EAAjB;;AACA,cAAMC,YAAY,GAAGC,gBAAKC,IAAL,CAAUL,QAAV,EAAoBtB,IAApB,CAArB;;AAEAK,QAAAA,eAAe,CACZ,oBAAmBN,WAAY,cAAaC,IAAK,MAAKyB,YAAa,EADvD,CAAf,CAJG,CAOH;;AACA,YAAI,CAACZ,aAAL,EAAoB;AAClB;AACA,cAAIE,uBAAJ,EAA6B;AAC3BF,YAAAA,aAAa,GAAG,MAAM,uCAAkBG,IAAlB,CAAtB;AACD,WAFD,MAEO;AACLH,YAAAA,aAAa,GAAG,MAAM,mCAAcH,GAAd,EAAmBM,IAAnB,CAAtB;AACD,WANiB,CAQlB;;;AACA,cAAI,CAACH,aAAL,EAAoB;AAClBN,+BAAOqB,IAAP,CACE,6DADF;;AAGArB,+BAAOY,IAAP,CACE,sIADF;AAGD;AACF,SAzBE,CA2BH;;;AACA,cAAMU,MAAM,GAAG,MAAM,yCACnBJ,YADmB,EAEnBZ,aAFmB,EAGnB,cAHmB,CAArB;AAMA,cAAMiB,mBAAmB,GAAI,GAAEnB,OAAQ,IAAGe,gBAAKK,QAAL,CACxC/B,IADwC,EAExC,aAFwC,CAGxC,iBAHF;;AAIAgC,sBAAGC,aAAH,CACEH,mBADF,EAEEI,IAAI,CAACC,SAAL,CAAeN,MAAf,EAAuBO,SAAvB,EAAkC,CAAlC,CAFF,EAGE,OAHF;;AAKA7B,2BAAOc,OAAP,CACG,0EAAyES,mBAAoB,EADhG;AAGD;AACF,GA3ED,CA2EE,OAAO3B,CAAP,EAAU;AACV,UAAMA,CAAN;AACD;AACF", "sourcesContent": ["import {Config} from '@react-native-community/cli-types';\nimport {execSync} from 'child_process';\nimport {logger, CLIError} from '@react-native-community/cli-tools';\nimport fs from 'fs';\nimport path from 'path';\nimport os from 'os';\nimport transformer from 'hermes-profile-transformer';\nimport {findSourcemap, generateSourcemap} from './sourcemapUtils';\nimport {\n  getAndroidProject,\n  getPackageName,\n} from '@react-native-community/cli-platform-android';\n/**\n * Get the last modified hermes profile\n * @param packageName\n */\nfunction getLatestFile(packageName: string): string {\n  try {\n    const file = execSync(`adb shell run-as ${packageName} ls cache/ -tp | grep -v /$ | grep -E '.cpuprofile' | head -1\n        `);\n    return file.toString().trim();\n  } catch (e) {\n    throw new Error(e);\n  }\n}\n\nfunction execSyncWithLog(command: string) {\n  logger.debug(`${command}`);\n  return execSync(command);\n}\n\n/**\n * Pull and convert a Hermes tracing profile to Chrome tracing profile\n * @param ctx\n * @param dstPath\n * @param fileName\n * @param sourceMapPath\n * @param raw\n * @param generateSourceMap\n */\nexport async function downloadProfile(\n  ctx: Config,\n  dstPath: string,\n  filename?: string,\n  sourcemapPath?: string,\n  raw?: boolean,\n  shouldGenerateSourcemap?: boolean,\n  port?: string,\n) {\n  try {\n    const androidProject = getAndroidProject(ctx);\n    const packageName = getPackageName(androidProject);\n\n    // If file name is not specified, pull the latest file from device\n    const file = filename || getLatestFile(packageName);\n    if (!file) {\n      throw new CLIError(\n        'There is no file in the cache/ directory. Did you record a profile from the developer menu?',\n      );\n    }\n\n    logger.info(`File to be pulled: ${file}`);\n\n    // If destination path is not specified, pull to the current directory\n    dstPath = dstPath || ctx.root;\n\n    logger.debug('Internal commands run to pull the file:');\n\n    // If --raw, pull the hermes profile to dstPath\n    if (raw) {\n      execSyncWithLog(\n        `adb shell run-as ${packageName} cat cache/${file} > ${dstPath}/${file}`,\n      );\n      logger.success(`Successfully pulled the file to ${dstPath}/${file}`);\n    }\n\n    // Else: transform the profile to Chrome format and pull it to dstPath\n    else {\n      const osTmpDir = os.tmpdir();\n      const tempFilePath = path.join(osTmpDir, file);\n\n      execSyncWithLog(\n        `adb shell run-as ${packageName} cat cache/${file} > ${tempFilePath}`,\n      );\n      // If path to source map is not given\n      if (!sourcemapPath) {\n        // Get or generate the source map\n        if (shouldGenerateSourcemap) {\n          sourcemapPath = await generateSourcemap(port);\n        } else {\n          sourcemapPath = await findSourcemap(ctx, port);\n        }\n\n        // Run without source map\n        if (!sourcemapPath) {\n          logger.warn(\n            'Cannot find source maps, running the transformer without it',\n          );\n          logger.info(\n            'Instructions on how to get source maps: set `bundleInDebug: true` in your app/build.gradle file, inside the `project.ext.react` map.',\n          );\n        }\n      }\n\n      // Run transformer tool to convert from Hermes to Chrome format\n      const events = await transformer(\n        tempFilePath,\n        sourcemapPath,\n        'index.bundle',\n      );\n\n      const transformedFilePath = `${dstPath}/${path.basename(\n        file,\n        '.cpuprofile',\n      )}-converted.json`;\n      fs.writeFileSync(\n        transformedFilePath,\n        JSON.stringify(events, undefined, 4),\n        'utf-8',\n      );\n      logger.success(\n        `Successfully converted to Chrome tracing format and pulled the file to ${transformedFilePath}`,\n      );\n    }\n  } catch (e) {\n    throw e;\n  }\n}\n"]}