{"version": 3, "names": ["HEALTHCHECK_TYPES", "ERROR", "WARNING", "getHealthchecks", "contributor", "additionalChecks", "projectSpecificHealthchecks", "config", "loadConfigAsync", "healthChecks", "reactNativePath", "common", "label", "healthchecks", "packager", "android", "androidSDK", "process", "platform", "ios", "xcodeEnv", "logger", "log", "info", "defaultHealthchecks", "nodeJS", "yarn", "npm", "watchman", "adb", "jdk", "androidStudio", "androidHomeEnvVariable", "gradle", "androidNDK", "xcode", "ruby", "cocoaPods", "iosDeploy", "deepmerge"], "sources": ["../../../src/tools/healthchecks/index.ts"], "sourcesContent": ["import nodeJS from './nodeJS';\nimport {yarn, npm} from './packageManagers';\nimport adb from './adb';\nimport jdk from './jdk';\nimport watchman from './watchman';\nimport ruby from './ruby';\nimport androidHomeEnvVariable from './androidHomeEnvVariable';\nimport androidStudio from './androidStudio';\nimport androidSDK from './androidSDK';\nimport androidNDK from './androidNDK';\nimport xcode from './xcode';\nimport cocoaPods from './cocoaPods';\nimport iosDeploy from './iosDeploy';\nimport {Healthchecks, HealthCheckCategory} from '../../types';\nimport {loadConfigAsync} from '@react-native-community/cli-config';\nimport xcodeEnv from './xcodeEnv';\nimport packager from './packager';\nimport gradle from './gradle';\nimport deepmerge from 'deepmerge';\nimport {logger} from '@react-native-community/cli-tools';\n\nexport const HEALTHCHECK_TYPES = {\n  ERROR: 'ERROR',\n  WARNING: 'WARNING',\n};\n\ntype Options = {\n  fix: boolean | void;\n  contributor: boolean | void;\n};\n\nexport const getHealthchecks = async ({\n  contributor,\n}: Options): Promise<Healthchecks> => {\n  let additionalChecks: HealthCheckCategory[] = [];\n  let projectSpecificHealthchecks = {};\n  let config;\n\n  // Doctor can run in a detached mode, where there isn't a config so this can fail\n  try {\n    config = await loadConfigAsync({});\n    additionalChecks = config.healthChecks;\n\n    if (config.reactNativePath) {\n      projectSpecificHealthchecks = {\n        common: {\n          label: 'Common',\n          healthchecks: [packager],\n        },\n        android: {\n          label: 'Android',\n          healthchecks: [androidSDK],\n        },\n        ...(process.platform === 'darwin' && {\n          ios: {\n            label: 'iOS',\n            healthchecks: [xcodeEnv],\n          },\n        }),\n      };\n    }\n  } catch {}\n\n  if (!config) {\n    logger.log();\n    logger.info(\n      'Detected that command has been run outside of React Native project, running basic healthchecks.',\n    );\n  }\n\n  const defaultHealthchecks = {\n    common: {\n      label: 'Common',\n      healthchecks: [\n        nodeJS,\n        yarn,\n        npm,\n        ...(process.platform === 'darwin' ? [watchman] : []),\n      ],\n    },\n    android: {\n      label: 'Android',\n      healthchecks: [\n        adb,\n        jdk,\n        androidStudio,\n        androidHomeEnvVariable,\n        gradle,\n        ...(contributor ? [androidNDK] : []),\n      ],\n    },\n    ...(process.platform === 'darwin'\n      ? {\n          ios: {\n            label: 'iOS',\n            healthchecks: [xcode, ruby, cocoaPods, iosDeploy],\n          },\n        }\n      : {}),\n    ...additionalChecks,\n  };\n\n  return deepmerge(defaultHealthchecks, projectSpecificHealthchecks);\n};\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAyD;AAElD,MAAMA,iBAAiB,GAAG;EAC/BC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE;AACX,CAAC;AAAC;AAOK,MAAMC,eAAe,GAAG,OAAO;EACpCC;AACO,CAAC,KAA4B;EACpC,IAAIC,gBAAuC,GAAG,EAAE;EAChD,IAAIC,2BAA2B,GAAG,CAAC,CAAC;EACpC,IAAIC,MAAM;;EAEV;EACA,IAAI;IACFA,MAAM,GAAG,MAAM,IAAAC,4BAAe,EAAC,CAAC,CAAC,CAAC;IAClCH,gBAAgB,GAAGE,MAAM,CAACE,YAAY;IAEtC,IAAIF,MAAM,CAACG,eAAe,EAAE;MAC1BJ,2BAA2B,GAAG;QAC5BK,MAAM,EAAE;UACNC,KAAK,EAAE,QAAQ;UACfC,YAAY,EAAE,CAACC,iBAAQ;QACzB,CAAC;QACDC,OAAO,EAAE;UACPH,KAAK,EAAE,SAAS;UAChBC,YAAY,EAAE,CAACG,mBAAU;QAC3B,CAAC;QACD,IAAIC,OAAO,CAACC,QAAQ,KAAK,QAAQ,IAAI;UACnCC,GAAG,EAAE;YACHP,KAAK,EAAE,KAAK;YACZC,YAAY,EAAE,CAACO,iBAAQ;UACzB;QACF,CAAC;MACH,CAAC;IACH;EACF,CAAC,CAAC,MAAM,CAAC;EAET,IAAI,CAACb,MAAM,EAAE;IACXc,kBAAM,CAACC,GAAG,EAAE;IACZD,kBAAM,CAACE,IAAI,CACT,iGAAiG,CAClG;EACH;EAEA,MAAMC,mBAAmB,GAAG;IAC1Bb,MAAM,EAAE;MACNC,KAAK,EAAE,QAAQ;MACfC,YAAY,EAAE,CACZY,eAAM,EACNC,qBAAI,EACJC,oBAAG,EACH,IAAIV,OAAO,CAACC,QAAQ,KAAK,QAAQ,GAAG,CAACU,iBAAQ,CAAC,GAAG,EAAE,CAAC;IAExD,CAAC;IACDb,OAAO,EAAE;MACPH,KAAK,EAAE,SAAS;MAChBC,YAAY,EAAE,CACZgB,YAAG,EACHC,YAAG,EACHC,sBAAa,EACbC,+BAAsB,EACtBC,eAAM,EACN,IAAI7B,WAAW,GAAG,CAAC8B,mBAAU,CAAC,GAAG,EAAE,CAAC;IAExC,CAAC;IACD,IAAIjB,OAAO,CAACC,QAAQ,KAAK,QAAQ,GAC7B;MACEC,GAAG,EAAE;QACHP,KAAK,EAAE,KAAK;QACZC,YAAY,EAAE,CAACsB,cAAK,EAAEC,aAAI,EAAEC,kBAAS,EAAEC,kBAAS;MAClD;IACF,CAAC,GACD,CAAC,CAAC,CAAC;IACP,GAAGjC;EACL,CAAC;EAED,OAAO,IAAAkC,oBAAS,EAACf,mBAAmB,EAAElB,2BAA2B,CAAC;AACpE,CAAC;AAAC"}