{"name": "@react-native-community/cli-doctor", "version": "15.1.3", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "types": "build/index.d.ts", "dependencies": {"@react-native-community/cli-config": "15.1.3", "@react-native-community/cli-platform-android": "15.1.3", "@react-native-community/cli-platform-apple": "15.1.3", "@react-native-community/cli-platform-ios": "15.1.3", "@react-native-community/cli-tools": "15.1.3", "chalk": "^4.1.2", "command-exists": "^1.2.8", "deepmerge": "^4.3.0", "envinfo": "^7.13.0", "execa": "^5.0.0", "node-stream-zip": "^1.9.1", "ora": "^5.4.1", "semver": "^7.5.2", "strip-ansi": "^5.2.0", "wcwidth": "^1.0.1", "yaml": "^2.2.1"}, "files": ["build", "!*.d.ts", "!*.map"], "devDependencies": {"@react-native-community/cli-types": "15.1.3", "@types/command-exists": "^1.2.0", "@types/envinfo": "^7.8.4", "@types/ip": "^1.1.0", "@types/prompts": "^2.4.4", "@types/semver": "^6.0.2", "@types/wcwidth": "^1.0.0"}, "homepage": "https://github.com/react-native-community/cli/tree/main/packages/cli-doctor", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-doctor"}, "gitHead": "926f1b94f697999acb3bded64c773daed533e238"}