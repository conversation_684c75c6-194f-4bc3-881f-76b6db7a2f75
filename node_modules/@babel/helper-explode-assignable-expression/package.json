{"name": "@babel/helper-explode-assignable-expression", "version": "7.18.6", "description": "Helper function to explode an assignable expression", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-explode-assignable-expression"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-explode-assignable-expression", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/types": "^7.18.6"}, "devDependencies": {"@babel/traverse": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}