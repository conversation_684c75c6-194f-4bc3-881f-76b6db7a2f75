{"name": "@babel/plugin-proposal-async-generator-functions", "version": "7.20.7", "description": "Turn async generator functions into ES2015 generators", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-async-generator-functions"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-async-generator-functions", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-remap-async-to-generator": "^7.18.9", "@babel/plugin-syntax-async-generators": "^7.8.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.20.7", "@babel/helper-plugin-test-runner": "^7.18.6", "babel-plugin-polyfill-corejs3": "^0.6.0", "core-js-pure": "^3.25.1"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}