{"name": "@babel/helper-simple-access", "version": "7.18.6", "description": "Babel helper for ensuring that access to a given value is performed through simple accesses", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-helper-simple-access", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-simple-access"}, "main": "./lib/index.js", "dependencies": {"@babel/types": "^7.18.6"}, "devDependencies": {"@babel/traverse": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}