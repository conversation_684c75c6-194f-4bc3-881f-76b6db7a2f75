{"name": "@babel/plugin-proposal-unicode-property-regex", "version": "7.18.6", "description": "Compile Unicode property escapes in Unicode regular expressions to ES5.", "homepage": "https://babel.dev/docs/en/next/babel-plugin-proposal-unicode-property-regex", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "engines": {"node": ">=4"}, "keywords": ["babel-plugin", "regex", "regexp", "regular expressions", "unicode properties", "unicode"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-proposal-unicode-property-regex"}, "bugs": "https://github.com/babel/babel/issues", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}