// Fixed ReactJSX
let reactJsxTransformPlugin = require('@babel/plugin-transform-react-jsx');
let parse = require('@babel/parser').parse;
let template = require('@babel/template').default;
const md5 = require('md5');
const { join } = require('path');
const { writeFileSync, existsSync, writeFile, mkdirSync, readFileSync } = require('fs');

reactJsxTransformPlugin.__esModule = true;
let _reactJsxTransformPluginDefault = reactJsxTransformPlugin.default;
reactJsxTransformPlugin['default'] = function (_ref) {
    let t = _ref.types;
    let res = _reactJsxTransformPluginDefault(_ref);
    let ProgramExitFn = res.visitor.Program.exit;
    res.visitor.Program.exit = function (path, state) {
        state.opts.pragma = '(this && this.createElement || React.createElement)';
        if (ProgramExitFn) {
            ProgramExitFn(path, state);
        }
        state.set('jsxIdentifier', function () {
            let ast = parse('this && this.createElement || React.createElement');
            return ast.program.body[0].expression;
        });
    };
    return res;
};

const Q_CLASS = ['QView', 'QComponent'];
const QRNPAGEMAP_FILEPATH_JSON_PATH = join(process.cwd(), './QRNPAGEMAP_FILEPATH.json'),
    QRNPAGEMAP_ID_JSON_PATH = join(process.cwd(), './QRNPAGEMAP_ID.json');

let QRNPAGEMAP_FILEPATH = null,
    QRNPAGEMAP_ID = null;

const targetPath = join(process.cwd(), './.qrnNewIdDir/');

if (existsSync(QRNPAGEMAP_FILEPATH_JSON_PATH) && existsSync(targetPath)) {
    QRNPAGEMAP_FILEPATH = require(QRNPAGEMAP_FILEPATH_JSON_PATH);
    QRNPAGEMAP_ID = require(QRNPAGEMAP_ID_JSON_PATH);
}

const buildQrnReactClassPlugin = (babel) => {
    const t = babel.types;
    return {
        visitor: {
            /**
             * 此 ExportDefaultDeclaration 针对写法一进行修复
             *
             * ```js
             * // 写法一
             * @observer
             * export default class Demo extends QView {}
             *
             * // 写法二
             * @observer
             * class Demo extends QView {}
             * export default Demo
             *
             * // 写法三
             * export default class Demo extends QView {}
             * ```
             *
             * 在上面写法一中, 会导致下方的 ClassDeclaration 没有进入
             * 编译的中间过程 `export default @observer class Demo extends QView{}`
             * 会有 export, 所以此时用 ExportDefaultDeclaration
             * 方法去找 class 进行操作
             */
            ExportDefaultDeclaration: {
                enter(path, state) {
                    if (path.node && path.node.declaration) {
                        const declaration = path.node.declaration;
                        if (t.isClassDeclaration(declaration)) {
                            const classNode = path.get('declaration').node;
                            insertExtByClassNode(classNode, path, state, t);
                        }
                    }
                },
            },
            ClassDeclaration(path, state) {
                const classNode = path.node;

                insertExtByClassNode(classNode, path, state, t);

                // 生成 page id
                if (!QRNPAGEMAP_FILEPATH || !QRNPAGEMAP_ID) {
                    return;
                }

                const className = classNode.id ? classNode.id.name : null;
                const { filename, cwd } = state;
                const pagePath =
                    filename.indexOf('/node_modules') < 0
                        ? filename.replace(process.env.HOME, '')
                        : filename.replace(cwd, '');

                const theClass = `${pagePath}/${className}`;
                const isExist = QRNPAGEMAP_FILEPATH[theClass] || null;

                let pageId = '';

                if (isExist) {
                    pageId = isExist.id;
                } else {
                    const theNewFile = join(targetPath, `./${md5(theClass)}.json`);

                    pageId = genPageId(theClass);

                    const newData = {
                        id: pageId,
                        class: theClass,
                        type: 2,
                        desc: '',
                        file_path: pagePath,
                    };

                    writeFileSync(theNewFile, JSON.stringify(newData, null, 4));
                }

                if (classNode.body.body) {
                    classNode.body.body.forEach((bd) => {
                        if (bd.type === 'ClassMethod' && bd.key.name === 'render') {
                            path.node.body.body.push(t.classProperty(t.stringLiteral('_ID_'), t.stringLiteral(pageId)));
                        }
                    });
                }
            },
        },
    };
};

const buildReactClass = template(`CLASS_NAME = Ext ? Ext.register(CLASS_NAME, CLASS_NAME_STR) : CLASS_NAME;`);
function insertExtByClassNode(classNode, path, state, t) {
    if (classNode) {
        /**
         * 挂载 __VISITED 属性, 防止重复插入 EXT 代码
         */
        const VISITED = classNode.__VISITED;
        const sc = classNode.superClass;
        const className = classNode.id ? classNode.id.name : null;

        if (sc && !VISITED) {
            // const root = path.findParent(t.isProgram);
            const superClassName = sc.name;
            const isExt = sc.object && sc.object.name === 'Ext';
            if (Q_CLASS.includes(superClassName) || isExt) {
                if (!className) {
                    throw new Error('QRN 抛错: 继承 QView 或 QComponent 不可为匿名类, 必须指定类名.');
                }
                // 在整个文件的末尾插入，避免 static 转换出来的变量丢失
                path.insertAfter(
                    buildReactClass({
                        CLASS_NAME: t.identifier(className),
                        CLASS_NAME_STR: t.stringLiteral(className),
                    })
                );
            }
            classNode.__VISITED = true;
        }
    }
}

const PAGE_ID_DICT = [
    '!',
    '#',
    '%',
    '&',
    ',',
    '(',
    ')',
    '*',
    '+',
    ',',
    '-',
    '.',
    '/',
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    ':',
    ';',
    '<',
    '=',
    '>',
    '?',
    '@',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
    '[',
    ']',
    '^',
    '-',
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
    '{',
    '|',
    '}',
    '~',
];

function genPageId(className) {
    let md5Str = md5(className);

    md5Str = md5Str.replace(/(.{8})/g, '$1,');
    const splitArr = md5Str.substring(0, md5Str.length - 2).split(',');
    console.log('splitArr: ', splitArr);

    let newId = '';

    splitArr.forEach((item) => {
        const leng = parseInt(item, 16);
        const idx = leng % PAGE_ID_DICT.length;

        newId += PAGE_ID_DICT[idx];
    });

    if (QRNPAGEMAP_ID[newId]) {
        newId = genPageId(className + newId);
    } else {
        QRNPAGEMAP_ID[newId] = true;
    }

    return newId;
}

module.exports = buildQrnReactClassPlugin;
