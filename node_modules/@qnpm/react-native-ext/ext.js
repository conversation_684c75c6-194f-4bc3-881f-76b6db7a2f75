import Ext from './lib/core.js';

// 必须先引入 redux
import './plugins/redux';

// 引入 router
import './plugins/router';

// 引入 webx
import './plugins/webx';

// 配置全局插件
Ext.defaults.globalPlugins = ['redux', 'router', 'webx'];

// 首次启动时执行前置操作 使用必须实现beforeRunApplication方法 并且一定要调用回调函数
Ext._beforeRunApplication = (runApplication) => {
    // Ext.beforeRunApplication 需要使用方实现
    if (Ext.beforeRunApplication && typeof Ext.beforeRunApplication === 'function') {
        try {
            Ext.beforeRunApplication(runApplication)
        } catch (error) {
            runApplication()
        }
    } else {
        runApplication()
    }
}

export default Ext;
