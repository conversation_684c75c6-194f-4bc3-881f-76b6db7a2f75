# 3.3.2 (January 22, 2020)
- Fix `React.memo` for v16.12+ (#93)

# 3.3.1 (November 14, 2019)
- Fix for UMD bundle (#85)
- Tooling changes (#83, #84, #87)

# 3.3.0 (January 23, 2019)
- Prevent hoisting of React.memo statics (#73)

# 3.2.1 (December 3, 2018)
- Fixed `defaultProps`, `displayName` and `propTypes` being hoisted from `React.forwardRef` to `React.forwardRef`. ([#71])

# 3.2.0 (November 26, 2018)
- Added support for `getDerivedStateFromError`. ([#68])
- Added support for React versions less than 0.14. ([#69])

# 3.1.0 (October 30, 2018)
- Added support for `contextType`. ([#62])
- Reduced bundle size. ([e89c7a6])
- Removed TypeScript definitions. ([#61])

# 3.0.1 (July 28, 2018)
- Fixed prop-types warnings. ([e0846fe])

# 3.0.0 (July 27, 2018)
- Dropped support for React versions less than 0.14. ([#55])
- Added support for `React.forwardRef` components. ([#55])

[#55]: https://github.com/mridgway/hoist-non-react-statics/pull/55
[#61]: https://github.com/mridgway/hoist-non-react-statics/pull/61
[#62]: https://github.com/mridgway/hoist-non-react-statics/pull/62
[#68]: https://github.com/mridgway/hoist-non-react-statics/pull/68
[#69]: https://github.com/mridgway/hoist-non-react-statics/pull/69
[#71]: https://github.com/mridgway/hoist-non-react-statics/pull/71
[e0846fe]: https://github.com/mridgway/hoist-non-react-statics/commit/e0846feefbad8b34d300de9966ffd607aacb81a3
[e89c7a6]: https://github.com/mridgway/hoist-non-react-statics/commit/e89c7a6168edc19eeadb2d149e600b888e8b0446
