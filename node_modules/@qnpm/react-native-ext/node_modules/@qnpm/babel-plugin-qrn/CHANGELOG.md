# CHANGELOG

## 1.0.1

- fixed: 修复由于写法一导致 EXT 代码没有插入的问题
    ```js
    // 写法一
    @observer
    export default class Demo extends QView {}

    // 写法二
    @observer
    class Demo extends QView {}
    export default Demo

    // 写法三
    export default class Demo extends QView {}
    ```

    增加的 ExportDefaultDeclaration 针对写法一进行修复
    在上面写法一中, 会导致下方的 ClassDeclaration 没有进入
    编译的中间过程 `export default @observer class Demo extends QView{}`
    会有 export, 所以此时用 ExportDefaultDeclaration
    方法去找 class 进行操作