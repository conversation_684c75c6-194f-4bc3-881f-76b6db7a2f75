// Fixed ReactJSX
let reactJsxTransformPlugin = require('@babel/plugin-transform-react-jsx');
let parse = require('@babel/parser').parse;
let template = require('@babel/template').default;

reactJsxTransformPlugin.__esModule = true;
let _reactJsxTransformPluginDefault = reactJsxTransformPlugin.default;
reactJsxTransformPlugin['default'] = function(_ref) {
    let t = _ref.types;
    let res = _reactJsxTransformPluginDefault(_ref);
    let ProgramExitFn = res.visitor.Program.exit;
    res.visitor.Program.exit = function(path, state) {
        state.opts.pragma = '(this && this.createElement || React.createElement)';
        ProgramExitFn(path, state);
        state.set('jsxIdentifier', function() {
            let ast = parse('this && this.createElement || React.createElement');
            return ast.program.body[0].expression;
        });
    };
    return res;
};


const Q_CLASS = ['QView', 'QComponent'];

const buildQrnReactClassPlugin = babel => {
    const t = babel.types;
    return {
        visitor: {
            /**
             * 此 ExportDefaultDeclaration 针对写法一进行修复
             *
             * ```js
             * // 写法一
             * @observer
             * export default class Demo extends QView {}
             *
             * // 写法二
             * @observer
             * class Demo extends QView {}
             * export default Demo
             *
             * // 写法三
             * export default class Demo extends QView {}
             * ```
             *
             * 在上面写法一中, 会导致下方的 ClassDeclaration 没有进入
             * 编译的中间过程 `export default @observer class Demo extends QView{}`
             * 会有 export, 所以此时用 ExportDefaultDeclaration
             * 方法去找 class 进行操作
             */
            ExportDefaultDeclaration: {
                enter(path, state) {
                    if (path.node && path.node.declaration) {
                        const declaration = path.node.declaration;
                        if (t.isClassDeclaration(declaration)) {
                            const classNode = path.get('declaration').node;
                            insertExtByClassNode(classNode, path, state, t);
                        }
                    }
                }
            },
            ClassDeclaration(path, state) {
                const classNode = path.node;
                insertExtByClassNode(classNode, path, state, t);
            }
        }
    };
};

const buildReactClass = template(`CLASS_NAME = Ext ? Ext.register(CLASS_NAME, CLASS_NAME_STR) : CLASS_NAME;`);
function insertExtByClassNode(classNode, path, state, t) {
    if (classNode && classNode.id) {
        /**
         * 挂载 __VISITED 属性, 防止重复插入 EXT 代码
         */
        const VISITED = classNode.__VISITED;
        const sc = classNode.superClass;
        const className = classNode.id.name;

        if (sc && !VISITED) {
            // const root = path.findParent(t.isProgram);
            const superClassName = sc.name;
            const isExt = sc.object && sc.object.name === 'Ext';
            if (Q_CLASS.includes(superClassName) || isExt) {
                // 在整个文件的末尾插入，避免 static 转换出来的变量丢失
                path.insertAfter(
                    buildReactClass({
                        CLASS_NAME: t.identifier(className),
                        CLASS_NAME_STR: t.stringLiteral(className)
                    })
                );
            }
            classNode.__VISITED = true;
        }
    }
}

module.exports = buildQrnReactClassPlugin;
