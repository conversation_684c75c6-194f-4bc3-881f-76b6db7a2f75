import React from 'react';
import { Provider } from '../../redux/lib/react-redux';
import PropTypes from 'prop-types';
import { View } from 'react-native';
import NavigationBar from '../navBar';
import ParamContext from '../../../lib/paramContext';

const { defineStore, usingRedux, invariant } = Ext.Redux;

const uniqueStoreSymbol = `This_view_has_it's_own_store.`;
const singletonStoreSymbol = `This_store_is_singleton.`;
// TODO: 临时去掉了缓存，没想好具体怎么加上去
// const renderedElementSymbol = `I_don't_want_be_rendered_again.`;

export default {
    title: null,
    /**
     * open 时设置标题
     * @param {string} title
     */
    setTitle(title) {
        this.title = title;
    },
    __storeObj: {},
    /**
     * 在组件的 onDestroy 生命周期销毁特定的 store
     * @param {Number} rootTag native 分配的 rootTag 值
     */
    clearStore(rootTag) {
        if (!!this.__storeObj[rootTag]) {
            this.__storeObj[rootTag] = null;
            delete this.__storeObj[rootTag];
        }
    },
    /**
     * 获取指定 VC 下的实时 store，如果没有就新建一个
     * @param {Number} rootTag
     * @return store
     */
    getStore(rootTag) {
        for (let start = rootTag - 10; start > 0; start -= 10) {
            if (!!this.__storeObj[start]) {
                return this.__storeObj[start];
            }
        }
        this.__storeObj[rootTag] = defineStore();
        return this.__storeObj[rootTag];
    },
    /**
     * 获取唯一的 store，如果没有就新建一个
     * @return store
     */
    getSingletonStore() {
        if (!this.__storeObj[singletonStoreSymbol]) {
            this.__storeObj[singletonStoreSymbol] = defineStore();
        }
        return this.__storeObj[singletonStoreSymbol];
    },
    wrapperRouter(Router) {
        let open = Router.open;
        Router.open = (viewName, options = {}) => {
            let [name, openParam] = (viewName || '').split(':');

            invariant(
                !openParam || usingRedux(),
                `在未配置 'Ext.defaults.redux.reducer' 的情况下不能使用 Router.open('view:%s') 的功能。`,
                openParam
            );

            // 只有 param 才会被 native 传回来
            if (!options.param) {
                options.param = {};
            }

            switch (openParam) {
                case 'new':
                    options.param[uniqueStoreSymbol] = true;
                    break;
            }
            return open.call(Router, name, options);
        };
    },
    shouldShowNavigationBar(Component) {
        let routerPlugin = Component.routerPlugin,
            globalNavBarOptions = Ext.defaults.navBar,
            isShow = globalNavBarOptions.isShow;

        if (routerPlugin && routerPlugin.isShow !== undefined) {
            isShow = routerPlugin.isShow;
        }

        return isShow;
    },
    wrapperView(className, Component) {
        // 缓存渲染过的页面
        if (!Component.routerPlugin) {
            Component.routerPlugin = {};
        }
        const renderedElementClass = (props) => {
            let { param = {}, rootTag, isQRCTShareStore = false } = props;

            let hasOwnStore = false;
            if (param[uniqueStoreSymbol]) {
                delete param[uniqueStoreSymbol];
                hasOwnStore = true;
            }
            // open 时传进来的 title
            if (!!this.title) {
                Component.routerPlugin.title = this.title;
                this.title = null;
            }
            if (usingRedux()) {
                let curStore = {};
                if (Ext.defaults.uesSingleStore) {
                    // 单一 store  项目级别的 store
                    curStore = this.getSingletonStore();
                } else if (isQRCTShareStore) {
                    // 共享 store
                    curStore = this.getStore(rootTag);
                } else {
                    // init store
                    curStore = this.__storeObj[rootTag] = defineStore();
                }
                if (hasOwnStore) {
                    const state = curStore.getState && curStore.getState();
                    curStore = defineStore(state);
                }
                // 渲染 Provider 包裹的 component
                return (
                    <Provider store={curStore}>
                        <View style={{ flexGrow: 1, flexShrink: 1 }}>
                            {this.shouldShowNavigationBar(Component) ? (
                                <NavigationBar
                                    {...Object.assign(
                                        {
                                            store: curStore,
                                            title: className,
                                            hashKey: `${className}_${rootTag}`,
                                            param: param,
                                        },
                                        Ext.defaults.navBar,
                                        Component.routerPlugin
                                    )}
                                />
                            ) : null}
                            <ParamContext.Provider value={param}>
                                <Component
                                    {...props}
                                    param={param}
                                    __rootTag_do_not_change_or_you_will_be_fired={rootTag}
                                />
                            </ParamContext.Provider>
                        </View>
                    </Provider>
                );
            }
            return (
                <View style={{ flexGrow: 1, flexShrink: 1 }}>
                    {this.shouldShowNavigationBar(Component) ? (
                        <NavigationBar
                            {...Object.assign(
                                {
                                    store: {},
                                    title: className,
                                    hashKey: `${className}_${rootTag}`,
                                    param: param,
                                },
                                Ext.defaults.navBar,
                                Component.routerPlugin
                            )}
                        />
                    ) : null}
                    <ParamContext.Provider value={param}>
                        <Component {...props} param={param} __rootTag_do_not_change_or_you_will_be_fired={rootTag} />
                    </ParamContext.Provider>
                </View>
            );
        };

        return (props) => renderedElementClass(props);
    },
};
