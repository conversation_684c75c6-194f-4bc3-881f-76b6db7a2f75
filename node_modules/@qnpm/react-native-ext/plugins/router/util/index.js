/**
 * @param {Object} data 
 * @param  {String} url - url
 * @return {Object}
 */
export const  mergeDataFromUrl = (data, url) => {
    let search = url.split('?')[1];

    if (search) {
        let pairs = search.split('&');

        pairs.forEach((pair) => {
            let pairArr = pair.split('=');
            let key = pairArr[0];
            let value = decodeURIComponent(pairArr[1]);

            try {
                value = JSON.parse(value);
            } catch (e) {

            }

            // url 的优先级高于 data
            data[key] = value;
        });
    }
    return data;
}