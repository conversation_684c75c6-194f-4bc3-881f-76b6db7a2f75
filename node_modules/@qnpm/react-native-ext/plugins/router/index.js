/**
 * React.Ext-Router
 * React.Ext 路由插件
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since   2017/10/16
 */

import { NativeModules, DeviceEventEmitter, BackHandler, AppRegistry } from 'react-native';
import 'qunar-react-native';
import mixRedux from './mix-redux';
import NavBar from './navBar';
import Bridge, { Router as NativeRouter } from './bridge.js';
import errorHandler from './util/errorHandler.js';
import { mergeDataFromUrl } from './util';

const rootTagKey = '__rootTag_do_not_change_or_you_will_be_fired';

let Router = {
    setTitle(title) {
        const hashKey = hasInvokedSetViewNameKey[currentViewHashKey];
        NavBar.setTitle(hashKey || currentViewHashKey, title);
    },
};

/**
 * 存放所有页面的容器
 * @type {Object}
 * @example
 * views = {
 *     pageA: {
 *         Component,   // viewClass
 *     }
 * }
 */
let views = {};

// 异步加载页面资源的函数工具
let viewsAsyncRequire = {};

// 已经分配的 tag -> viewName 的 map
let hasAllocatedRootTagMap = {};
// 已经执行了 setView 的页面，
// 为了导航栏设置标题和控制 modal mask 的区域
let hasInvokedSetViewNameKey = {};
// 是否是 Qunar React Native
let isQReact = !!NativeModules.QRCTDeviceInfo;

// 当前打开的页面的名字
let currentViewHashKey = '';

Ext.addPlugin(
    'router',
    function (context, pOpts = {}, React, isView) {
        const rootTag = (context.props && context.props[rootTagKey]) || '';
        if (!isView || !rootTag || hasAllocatedRootTagMap[rootTag]) {
            return;
        }

        const viewName = context.constructor.className;
        let bindEvents = context.bindEvents || {};
        let listenerViewKey = `${viewName}_${rootTag}`;
        let listeners = (Router._lifecycleListeners[listenerViewKey] = {});

        // 防止出现 QView 包 QView 的情况
        if (!!rootTag) {
            hasAllocatedRootTagMap[rootTag] = viewName;
        }
        if (typeof context.onBackPressed === 'function') {
            listeners['onBackPressed'] = context.onBackPressed.bind(context);
        }
        for (let key in bindEvents) {
            if (Ext.utils.isFunction(bindEvents[key])) {
                listeners[key] = bindEvents[key].bind(context);
            }
        }
    },
    null,
    function (Component, isView, plugins, className) {
        // 保存 className, 打包压缩之后找不到
        Component.className = className;
        // 针对 redux connect 封装的组件名字缓存处理
        if (Component.WrappedComponent) {
            Component.WrappedComponent.className = className;
        }
        // 获取 Component
        if (isView) {
            if (!Ext.defaults.indexView) {
                Ext.defaults.indexView = className;
                // 保留之前的 indexView 的配置方式，有个提示，需要在某版修改
                if (Ext.defaults.appName !== 'naive') {
                    console.warn('Ext.defaults.appName 不再使用，请及时删除该行配置');
                }
            }

            views[className] = { Component };

            AppRegistry.registerComponent(className, () => mixRedux.wrapperView(className, Component));
        }
    }
);

/*************************
 * Router API
 ************************/

/**
 * [API] open
 * @param  {String} name 页面名字
 * @param  {Object} opts 参数
 * @return
 */

Router.open = async (name, opts = {}) => {
    if (!views[name] && typeof viewsAsyncRequire[name] !== 'function') {
        errorHandler.noView(name);
        return false;
    }
    if (opts.title) {
        mixRedux.setTitle(opts.title);
        delete opts.title;
    }
    return await NativeRouter.open(name, opts);
};

/**
 * [API] back
 * @param  {Object} opts 参数
 * @return
 */
Router.back = async (opts = {}) => {
    return await NativeRouter.back(opts);
};

/**
 * [API] backTo
 * @param  {String} name 页面名字
 * @param  {Object} opts 参数
 * @return {Boolean}
 */
Router.backTo = async (name, opts = {}) => {
    return await NativeRouter.backTo(opts.hybridId, name, opts);
};

/**
 * [API] goto
 * @param  {String} name 页面名字
 * @param  {Object} opts 参数
 * @return
 */
Router.goto = async (name, opts = {}) => {
    return await NativeRouter.goto(name, opts);
};

/**
 * [API] home
 * @param  {Object} opts 参数
 * @return
 */
Router.home = async (opts = {}) => {
    return await NativeRouter.home(opts);
};

/**
 * [API] close
 * @param  {String} name 页面名字
 * @return
 */
Router.close = async (name) => {
    if (!name) {
        return await Router.back();
    }

    return await NativeRouter.close(name);
};

/**
 * [API] exit
 * @param  {Object} opts 参数
 * @return
 */
Router.exit = async (opts = {}) => {
    return await NativeRouter.exit(opts);
};

/*************************
 * Native Bridge
 ************************/

// 暂存 actived 参数
let activedParam = {};

function getListenerByNameType(name, type) {
    const listeners = Router._lifecycleListeners[name] || {};
    return listeners[type] || (() => {});
}

BackHandler.addEventListener('hardwareBackPress', (opts) => {
    if (opts && opts.name) {
        const { index, name } = opts;
        const listeners = Router._lifecycleListeners[`${name}_${index}`] || {};
        const onBackPressed = listeners['onBackPressed'];
        if (typeof onBackPressed === 'function' && onBackPressed()) {
            return true;
        }
        Router.back();
        return true;
    }
});

DeviceEventEmitter.addListener('onReady', (opts) => {
    const { name, index } = opts;
    getListenerByNameType(`${name}_${index}`, 'ready')();
});

DeviceEventEmitter.addListener('onInitData', (opts) => {
    const { name, index, data = null } = opts;
    activedParam[`${name}_${index}`] = data;
});

DeviceEventEmitter.addListener('onReceiveData', (opts) => {
    const { name, index, data } = opts;
    activedParam[`${name}_${index}`] = data;
});

DeviceEventEmitter.addListener('onShow', (opts) => {
    const { name, index } = opts;
    currentViewHashKey = `${name}_${index}`;
    getListenerByNameType(currentViewHashKey, 'actived')(activedParam[currentViewHashKey] || {});
    delete activedParam[currentViewHashKey];
});

DeviceEventEmitter.addListener('onHide', (opts) => {
    const { name, index } = opts;
    getListenerByNameType(`${name}_${index}`, 'deactived')();
});

DeviceEventEmitter.addListener('onDestroy', (opts) => {
    const { name, index } = opts;
    mixRedux.clearStore(index);
    getListenerByNameType(`${name}_${index}`, 'destroy')();
});

/*************************
 * 工具类方法
 ************************/

// @redux 强行包裹 Router.open 方法
mixRedux.wrapperRouter(Router);

// 暴露一下
Router.Bridge = Bridge;
Router._views = views;
Router._hasAllocatedRootTagMap = hasAllocatedRootTagMap;
Router._viewsAsyncRequire = viewsAsyncRequire;
// 每个 rn 页面的生命周期回调
Router._lifecycleListeners = {};
Router._hasInvokedSetViewNameKey = hasInvokedSetViewNameKey;

Ext.Router = Router;
Ext.Router.navBar = { setTitle: Ext.Router.setTitle };

Ext.open = Router.open;
Ext.back = Router.back;
Ext.backTo = Router.backTo;
Ext.goto = Router.goto;
Ext.home = Router.home;
Ext.close = Router.close;
Ext.exit = Router.exit;
Ext.setSwipeBackEnabled = (isEnabled) => {
    Bridge.setSwipeBackEnabled(isEnabled);
};

Ext.showNavigationBarMask = (opt = {}) => {
    const { opacity, onMaskPress } = opt;
    const hashKey = hasInvokedSetViewNameKey[currentViewHashKey];
    const navigationBar = NavBar.getInstanceByHashKey(hashKey || currentViewHashKey);
    if (navigationBar) {
        navigationBar.mask.show(opacity, onMaskPress);
    }
};

Ext.hideNavigationBarMask = () => {
    const hashKey = hasInvokedSetViewNameKey[currentViewHashKey];
    const navigationBar = NavBar.getInstanceByHashKey(hashKey || currentViewHashKey);
    if (navigationBar) {
        navigationBar.mask.hide();
    }
};

Ext.registerView = (name, callback) => {
    if (typeof callback !== 'function') {
        throw new Error(
            `Ext.registerView(name, callback) 中的 callback 参数必须是函数类型，现在传入的是 ${typeof callback}`
        );
        return;
    }

    // 等业务逻辑搞定时，执行一下 init
    if (!Ext._cache['View']) {
        Ext.init();
    }

    viewsAsyncRequire[name] = callback;

    // 上报页面注册
    if (global.__QP_INFO && global.QrnConfig && global.QrnConfig['qrnPageRegisteredReportEnable'] === true) {
        const { hybridid, version } = global.__QP_INFO;

        const qrnPagesData = {
            bizType: 'app',
            bizTag: 'APP',
            module: 'default',
            appcode: 'qrn_js',
            page: 'qrn_pages',
            id: 'qrn_pages_visited',
            operType: 'monitor',
            operTime: '*',
            ext: {
                hybridId: hybridid,
                pageName: name,
                version,
                rtag: global.__BTAG_BIZ || '',
            },
        };

        NativeModules.QAV && NativeModules.QAV.componentLog(qrnPagesData);
    }
};

Ext.lifecycleListeners = (props, listeners) => {
    let listenerName = props ? props['__qrnLifecycleListenerName'] : null;

    if (!listenerName) {
        throw new Error('props 不能为空 或者 props 中没有 __qrnLifecycleListenerName 属性');
    }

    if (typeof listeners !== 'object') {
        throw new Error('listeners 必须为对象类型');
    }

    // 遍历 listeners 每一个属性，每一个属性的值必须是一个函数
    Object.keys(listeners).forEach((key) => {
        if (typeof listeners[key] !== 'function') {
            throw new Error(key + ' 必须为函数类型');
        }
    });

    Router._lifecycleListeners[listenerName] = listeners;
};

export default Router;
