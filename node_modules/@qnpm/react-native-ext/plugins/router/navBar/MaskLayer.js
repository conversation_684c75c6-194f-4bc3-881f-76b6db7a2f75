/**
 * Created by Ellery1 on 16/8/9.
 */
import React, {Component} from 'react';
import { View, TouchableWithoutFeedback, Dimensions } from 'react-native';

const screenWidth = Dimensions.get('window').width;
const OPACITY = 0.4;
const noop = ()=> {
};

export default class MaskLayer extends Component {

    constructor(props) {
        super(props);
        this.state = {
            show: false,
            opacity: OPACITY,
            onMaskPress: noop
        };
    }

    show(opacity = OPACITY, onMaskPress = noop) {
        this.setState({show: true, opacity, onMaskPress});
    }

    hide() {
        this.setState({show: false, onMaskPress: noop});
    }

    render() {
        const {show, opacity, onMaskPress}=this.state;
        const {height}=this.props;

        return (
            show ?
                <TouchableWithoutFeedback onPress={()=>onMaskPress()}>
                    <View style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: screenWidth,
                        height: height,
                        opacity: opacity,
                        backgroundColor: '#000'
                    }}/>
                </TouchableWithoutFeedback> : null
        );
    }
}