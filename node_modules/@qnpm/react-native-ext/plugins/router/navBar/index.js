/**
 * Created by <PERSON>ry1 on 16/7/29.
 */

import React, {isValidElement, Component} from 'react';
import PropTypes from 'prop-types';
import styles from './styles';
import MaskLayer from './MaskLayer';
import errorHandler from '../util/errorHandler';
import { View, TouchableOpacity, Text } from 'react-native';
const _ = Ext.utils;

const defaultProps = {
    isShow: false,
    isImmersive: false,
    backgroundColor: '#00bcd4',
    color: '#fff',
    height: 44,
    statusBarBackgroundColor: 'transparent',
    activeOpacity: 0.6,
    title: '',
    buttonWidth: 60,
    leftButtonText: '返回',
    rightButtonText: '',
    leftButtonPressEvent(store, evt){
        Ext.Router.back();
    },
    rightButtonPressEvent(store, evt){
    }
};

const propTypes = {
    isShow: PropTypes.bool,
    isImmersive: PropTypes.bool,
    backgroundColor: PropTypes.string,
    color: PropTypes.string,
    height: PropTypes.number,
    statusBarBackgroundColor: PropTypes.string,
    activeOpacity: PropTypes.number,
    title: PropTypes.oneOfType([PropTypes.element, PropTypes.string, PropTypes.func]),
    leftButtonText: PropTypes.oneOfType([PropTypes.element, PropTypes.string, PropTypes.func]),
    rightButtonText: PropTypes.oneOfType([PropTypes.element, PropTypes.string, PropTypes.func]),
    leftButtonPressEvent: PropTypes.func,
    rightButtonPressEvent: PropTypes.func
};

class NavigationBar extends Component {

    static instances = [];

    static getInstanceByHashKey(key) {
        return NavigationBar.instances.find(instance=>instance.viewHashKey === key);
    }

    static setTitle(key, title) {
        const instance = NavigationBar.getInstanceByHashKey(key);
        if (instance) {
            instance.setTitle(title);
        }
    }

    constructor(props) {
        super(props);
        this.state = {title: props.title};
        this.viewHashKey = props.hashKey;
        NavigationBar.instances.push(this);
    }

    componentWillUnmount() {
        const index = NavigationBar.instances.indexOf(this);

        if (index !== -1) {
            NavigationBar.instances.splice(index, 1);
        }
    }

    getTextJSX(text, color, isTitle = false) {
        const {store}=this.props;

        return text ?
            _.isFunction(text) ?
                isTitle ?
                    <View style={styles.title}>{text.call(this, store)}</View> :
                    text.call(store) :
                isValidElement(text) ?
                    isTitle ?
                        <View style={styles.title}>{text}</View> :
                        text :
                    <Text numberOfLines={1}
                          style={[styles.text, {color}, isTitle ? styles.titleText : null]}>{text}</Text> :
            null;
    }

    setTitle(title) {
        this.setState({title});
    }

    render() {
        const {leftButtonText, rightButtonText}=this.props;
        const {title}=this.state;
        const {leftButtonPressEvent, rightButtonPressEvent}=this.props;
        const {activeOpacity, color}=this.props;
        const {store}=this.props;
        const leftJSX = this.getTextJSX(leftButtonText, color),
            rightJSX = this.getTextJSX(rightButtonText, color),
            titleJSX = this.getTextJSX(title, color, true);
        const {height, backgroundColor, buttonWidth, statusBarBackgroundColor}=this.props;
        const {isImmersive} = this.props;
        if (height === 0) {
            errorHandler.warn('当前导航栏高度为 0，如需隐藏导航栏，请将 Ext.defaults.navBar.isShow 设置成 false');
        }
        // buttonWidth 为 0 时，ios 导航栏显示有问题
        if ( buttonWidth === 0) {
            errorHandler.warn('当前导航栏两侧按钮宽度为 0，如需隐藏按钮，请将对应按钮的文本内容置空');
        }
        return (
            <View style={{backgroundColor}}>
                {!isImmersive ? <View style={[styles.statusBar, {backgroundColor: statusBarBackgroundColor}]}/> : null}
                <View style={[styles.bar, {height}]}>
                    <TouchableOpacity style={[styles.left, {width: buttonWidth, height}]}
                                      onPress={evt=>leftButtonPressEvent.call(this, store, evt)}
                                      activeOpacity={activeOpacity}>
                        {leftJSX}
                    </TouchableOpacity>
                    {titleJSX}
                    <TouchableOpacity style={[styles.right, {width: buttonWidth, height}]}
                                      onPress={evt=>rightButtonPressEvent.call(this, store, evt)}
                                      activeOpacity={activeOpacity}>
                        {rightJSX}
                    </TouchableOpacity>
                </View>
                <MaskLayer ref={mask=>{if(mask) this.mask=mask}} height={height + styles.statusBar.height}/>
            </View>
        );
    }
}

NavigationBar.defaultProps = defaultProps;
NavigationBar.propTypes = propTypes;

export default NavigationBar;
