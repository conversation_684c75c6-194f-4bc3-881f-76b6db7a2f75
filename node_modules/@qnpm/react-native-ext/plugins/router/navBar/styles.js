/**
 * Created by Ellery1 on 16/7/29.
 */
import { Dimensions, NativeModules, Platform } from 'react-native';
const fitSystemWindowHeight = Platform.OS === 'android' ? NativeModules.QRCTDisplayInfo.fitSystemWindowHeight : null;
const statusBarHeight = Dimensions.get('window').statusBarHeight;

export default {
    statusBar: {
        height: Platform.OS === 'ios' ? statusBarHeight : fitSystemWindowHeight
    },
    bar: {
        flexGrow: 1,
        flexShrink: 1,
        flexDirection: 'row',
        alignItems: 'center'
    },
    left: {
        width: 60,
        paddingLeft: 10,
        alignItems: 'flex-start',
        justifyContent: 'center'
    },
    title: {
        flexGrow: 1,
        flexShrink: 1,
        alignItems: 'center',
        overflow: 'hidden',
        flexDirection: 'column'
    },
    right: {
        width: 60,
        paddingRight: 10,
        alignItems: 'flex-end',
        justifyContent: 'center'
    },
    text: {
        color: '#fff'
    },
    titleText: {
        flexGrow: 1,
        flexShrink: 1,
        overflow: 'hidden',
        flexDirection: 'column',
        textAlign: 'center'
    }
};

