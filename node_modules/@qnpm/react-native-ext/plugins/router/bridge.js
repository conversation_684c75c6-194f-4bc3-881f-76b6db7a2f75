import { NativeModules } from 'react-native';
import { mergeDataFromUrl } from './util';
import errorHandler from './util/errorHandler.js';

let Bridge = {
    // 发送广播
    sendBroadcast(opts) {
        callNativeAPI('QRCTBroadCastManager', 'sendBroadcast', [opts.name, opts.data, opts.hybridId || '']);
    },
    // 发送 schema
    sendSchema(opts, cb = () => { }) {
        console.warn('sendSchema 已纠正为 sendScheme，该版本已移除');
        callNativeAPI('QRCTJumpHandleManager', 'sendSchema', [opts.url, opts.data, opts.adrToken || '', cb]);
    },
    // 发送 scheme
    sendScheme(opts, cb = () => { }) {
        callNativeAPI('QRCTJumpHandleManager', 'sendScheme', [opts.url, opts.data, opts.adrToken || '', cb]);
    },
    sendDeepLinkOrScheme(opts, cb = () => { }) {
        callNativeAPI('QRCTJumpHandleManager', 'sendDeepLinkOrScheme', [opts.deepLink, opts.url, opts.data, opts.adrToken || '', cb]);
    },
    // 关闭当前 RN VC
    closeCurrentVC() {
        callNativeAPI('QRCTVCManager', 'closeCurrentVC', [{}]);
    },
    // 之行 Native 函数
    sendNativeEvents(opts) {
        callNativeAPI('QRCTNativeCallbackManager', 'sendNativeEvents', [opts.id, opts.data || {}]);
    },
    // 打开新的 VC
    openNewVC(opts) {
        const { data = {} } = opts;
        if (typeof data.param === 'number') data.param = '' + data.param;
        callNativeAPI('QRCTVCManager', 'openNewVC', [opts.hybridId || Ext.defaults.hybridId || '', opts.moduleName || Ext.defaults.appName, data, opts.adrToken || '', {}]);
    },
    // 右滑手势开关
    setSwipeBackEnabled(isEnabled) {
        callNativeAPI('QRCTVCManager', 'setSwipeBackEnabledWithoutCallback', [isEnabled]);
    },
};

export const Router = {
    // open 打开新页面
    async open(name, opts) {
        if (typeof opts.param === 'number') opts.param = '' + opts.param;
        return await callNativeAPIAsync('QNavigation', 'open', [name, opts]);
    },
    // back 返回之前的页面
    async back(opts) {
        if (typeof opts.param === 'number') opts.param = '' + opts.param;
        return await callNativeAPIAsync('QNavigation', 'back', [opts]);
    },
    // backTo 返回到指定页面
    async backTo(hybridId, name, opts) {
        if (typeof opts.param === 'number') opts.param = '' + opts.param;
        return await callNativeAPIAsync('QNavigation', 'backTo', [hybridId || Ext.defaults.hybridId || '', name, opts]);
    },
    // goto
    async goto(name, opts) {
        if (typeof opts.param === 'number') opts.param = '' + opts.param;
        return await callNativeAPIAsync('QNavigation', 'gotoPage', [name, opts]);
    },
    // home 新页面
    async home(opts) {
        if (typeof opts.param === 'number') opts.param = '' + opts.param;
        return await callNativeAPIAsync('QNavigation', 'home', [opts]);
    },
    // close 关闭指定页面
    async close(name) {
        return await callNativeAPIAsync('QNavigation', 'close', [name]);
    },
    // home 退出当前 RN 项目
    async exit(opts) {
        if (typeof opts.param === 'number') opts.param = '' + opts.param;
        return await callNativeAPIAsync('QNavigation', 'exit', [opts]);
    },
    // setHomeModuleName 设置首次打开 RN 项目的 moduleName，已经在 AppRegistry.runApplication 中使用
    setHomeModuleName(name) {
        callNativeAPI('QNavigation', 'setHomeModuleName', [name]);
    },
    // setViewName 设置当前页面的别名
    setViewName(rootTag, name) {
        callNativeAPI('QNavigation', 'setViewName', [rootTag, name]);
    }
};

function callNativeAPI(namespace, APIName, args) {
    let NativeModule = NativeModules[namespace];
    if (NativeModule) {
        let API = NativeModule[APIName];
        API && API.apply(NativeModule, args);
    }
}

async function callNativeAPIAsync(namespace, APIName, args) {
    routerReport({ namespace, APIName });
    let NativeModule = NativeModules[namespace];
    if (NativeModule) {
        let API = NativeModule[APIName];
        let res = {};
        try {
            res = await API.apply(NativeModule, args);
        } catch (e) {
            errorHandler.nativeRouterError(args, e);
            return false;
        }
        if (!!res.status) {
            errorHandler.nativeRouterError(args, res.msg);
        }
        return res.ret;
    }
}

function routerReport(params) {
    const { namespace = '', APIName = '' } = params;
    const hybridId = global.__QP_INFO && global.__QP_INFO.hybridid;
    const qpVer = global.__QP_INFO && global.__QP_INFO.version;

    const baseData = {
        "ext": {
            hybridId,
            "methodName": `${namespace}-${APIName}`,
            "version": qpVer
        },
        "bizType": "app",
        "bizTag": "APP",
        "module": "qrnExtRouter",
        "appcode": "qrn_lib",
        "page": "qvideo_component",
        "id": "callNativeAPI",
        "operType": "click",
        "key": "app/qvideo_component/qrnExtRouter/click/callNativeAPI",
        "operTime": Date.now() + '',
    }

    NativeModules.QAV && NativeModules.QAV.componentLog && NativeModules.QAV.componentLog(baseData);
}

export default Bridge;
