#EXT CHANGE LOG
## v0.2.0
### 新增功能
#### NavigationBar
- 导航条组件完全重写,由全局单例变为每页一个独立的导航条
- leftButtonText/title/rightButtonText 现在可以接收字符串,JSX和返回JSX的函数作为参数
- leftButtonPress/rightButtonPress 现在可以接受route和store两个参数
- 新增按钮宽度配置项(buttonWidth)
- 新增状态栏背景色配置项(statusBarBackgroundColor)

### BUG修复
- 修复SetSwipeBack在Router.back时不生效的bug
- 修复sendScheme打开一个尚未mount的RN App时抛出异常的bug
- 修复调用Router.close关闭其他VC中的view不生效的bug

## v0.2.2
### BUG修复
- 修复了使用Router.back回退时,再用手势回退至同一view bindEvents生命周期失效的BUG
- 修复手势回退时页面重复mount的bug
- 修复了从另一个VC回退到home时,home不触发actived的bug