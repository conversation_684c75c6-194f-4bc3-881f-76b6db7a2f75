'use strict';

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _ponyfill = require('./pollyfill.js');

var _ponyfill2 = _interopRequireDefault(_ponyfill);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { 'default': obj }; }

var root; /* global window */


if (typeof self !== 'undefined') {
    root = self;
} else if (typeof window !== 'undefined') {
    root = window;
} else if (typeof global !== 'undefined') {
    root = global;
} else if (typeof module !== 'undefined') {
    root = module;
} else {
    root = Function('return this')();
}

var result = (0, _ponyfill2['default'])(root);
exports['default'] = result;