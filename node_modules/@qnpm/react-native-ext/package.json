{"name": "@qnpm/react-native-ext", "version": "4.1.3", "description": "React Native Extension", "main": "ext.js", "scripts": {"start": "node_modules/react-native/packager/packager.sh", "router": "node_modules/react-native/packager/packager.sh '--root' './example/router'", "webx": "node_modules/react-native/packager/packager.sh '--root' './example/webx'", "redux": "node_modules/react-native/packager/packager.sh '--root' './example/redux'", "waimai": "node_modules/react-native/packager/packager.sh '--root' './example/waimai'"}, "devDependencies": {"gulp": "^3.9.1", "gulp-shell": "^0.5.2", "gulp-webserver": "^0.9.1", "react-native": "0.61.0"}, "dependencies": {"@qnpm/babel-plugin-qrn": "1.0.3", "@babel/runtime": "^7.5.5", "hoist-non-react-statics": "^3.3.0", "invariant": "^2.2.4", "loose-envify": "^1.4.0", "prop-types": "^15.7.2", "react-is": "^16.9.0"}}