import React from 'react';
import { Router as NativeRouter } from '../plugins/router/bridge.js';
import { DeviceEventEmitter, NativeModules } from 'react-native';

// Const
const rootTagKey = '__rootTag_do_not_change_or_you_will_be_fired';
const TRUE = true,
    FALSE = false,
    NULL = null,
    UNDEFINED = void 0;

const __object__ = Object.prototype,
    __array__ = Array.prototype,
    toString = __object__.toString,
    slice = __array__.slice;

// Variable

let defaults = {
    appName: 'naive',
    globalPlugins: [],
    navBar: { isShow: false }
};

let plugins = {};
let viewMap = {};
let initFns = [];
let registerCallbacks = [];

let Ext = { defaults };

let cache = {};

let nativeListenerMap = {};

// Util

const class2type = {
    '[object HTMLDocument]': 'Document',
    '[object HTMLCollection]': 'NodeList',
    '[object StaticNodeList]': 'NodeList',
    '[object IXMLDOMNodeList]': 'NodeList',
    '[object DOMWindow]': 'Window',
    '[object global]': 'Window',
    null: 'Null',
    NaN: 'NaN',
    undefined: 'Undefined'
};

'Boolean,Number,String,Function,Array,Date,RegExp,Window,Document,Arguments,NodeList,Null,Undefined'.replace(
    /\w+/gi,
    (value) => (class2type[`[object ${value}]`] = value)
);

let getType = (obj, match) => {
    let rs = class2type[obj === NULL || obj !== obj ? obj : toString.call(obj)] || (obj && obj.nodeName) || '#';
    if (obj === UNDEFINED) {
        rs = 'Undefined';
    } else if (rs.charAt(0) === '#') {
        if (obj == obj.document && obj.document != obj) {
            rs = 'Window';
        } else if (obj.nodeType === 9) {
            rs = 'Document';
        } else if (obj.callee) {
            rs = 'Arguments';
        } else if (isFinite(obj.size || obj.length) && obj.item) {
            rs = 'NodeList';
        } else {
            rs = toString.call(obj).slice(8, -1);
        }
    }
    if (match) {
        return match === rs;
    }
    return rs;
};

let _isObject = (source) => getType(source, 'Object');
let _isArray = (source) => getType(source, 'Array');
let _isString = (source) => getType(source, 'String');
let _isFunction = (source) => getType(source, 'Function');
let _isNumber = (source) => getType(source, 'Number');
let _isPlainObject = (source) => getType(source, 'Object') && Object.getPrototypeOf(source) === __object__;
let _isEmptyObject = (source) => {
    try {
        return JSON.stringify(source) === '{}';
    } catch (e) {
        return FALSE;
    }
};
let _noop = () => {};

let _sliceFn = (obj, attr, fn) => {
    let originFn = obj[attr];
    obj[attr] = function () {
        fn.apply(this, arguments);
        if (_isFunction(originFn)) {
            return originFn.apply(this, arguments);
        }
    };
};

let extend = (target, source, deep) => {
    for (let key in source) {
        if (deep && (_isPlainObject(source[key]) || _isArray(source[key]))) {
            if (_isPlainObject(source[key]) && !_isPlainObject(target[key])) {
                target[key] = {};
            }
            if (_isArray(source[key]) && !_isArray(target[key])) {
                target[key] = [];
            }
            extend(target[key], source[key], deep);
        } else if (source[key] !== UNDEFINED) {
            target[key] = source[key];
        }
    }
};

let _extend = function () {
    let deep,
        args = _makeArray(arguments),
        target = args.shift();
    if (typeof target == 'boolean') {
        deep = target;
        target = args.shift();
    }
    args.forEach((arg) => extend(target, arg, deep));
    return target;
};

let _startWith = (str, sch) => str.indexOf(sch) == 0;

let _endWith = (str, sch) => str.indexOf(sch) > -1 && str.indexOf(sch) == str.length - sch.length;

// CustEvent

let _once = (func) => {
    let ran = FALSE,
        memo;
    return function () {
        if (ran) return memo;
        ran = TRUE;
        memo = func.apply(this, arguments);
        func = NULL;
        return memo;
    };
};

let triggerEvents = (events, args) => {
    let ev,
        i = -1,
        l = events.length,
        ret = 1;
    while (++i < l && ret) {
        ev = events[i];
        ret &= ev.callback && ev.callback.apply(ev.ctx, args) !== FALSE;
    }
    return !!ret;
};

let CustEvent = {
    on(name, callback, context) {
        this._events = this._events || {};
        this._events[name] = this._events[name] || [];
        let events = this._events[name];
        events.push({
            callback: callback,
            context: context,
            ctx: context || this
        });
        return this;
    },
    once(name, callback, context) {
        let self = this;
        let once = _once(function () {
            self.off(name, once);
            callback.apply(self, arguments);
        });
        once._callback = callback;
        return this.on(name, once, context);
    },
    off(name, callback, context) {
        var retain, ev, events, names, i, l, j, k;
        if (!name && !callback && !context) {
            this._events = UNDEFINED;
            return this;
        }
        names = name ? [name] : keys(this._events);
        for (i = 0, l = names.length; i < l; i++) {
            name = names[i];
            events = this._events[name];
            if (events) {
                this._events[name] = retain = [];
                if (callback || context) {
                    for (j = 0, k = events.length; j < k; j++) {
                        ev = events[j];
                        if (
                            (callback && callback !== ev.callback && callback !== ev.callback._callback) ||
                            (context && context !== ev.context)
                        ) {
                            retain.push(ev);
                        }
                    }
                }
                if (!retain.length) delete this._events[name];
            }
        }
        return this;
    },
    trigger(name) {
        if (!this._events) return this;
        let args = slice.call(arguments, 1),
            events = this._events[name],
            allEvents = this._events.all,
            ret = 1;
        if (events) {
            ret &= triggerEvents(events, args);
        }
        if (allEvents && ret) {
            ret &= triggerEvents(allEvents, args);
        }
        return !!ret;
    }
};

let _createEventManager = () => {
    let EM = function () {};
    _extend(EM.prototype, CustEvent);
    return new EM();
};

let _makeArray = (iterable) => {
    if (!iterable) return FALSE;
    var n = iterable.length;
    if (n === n >>> 0) {
        try {
            return slice.call(iterable);
        } catch (e) {}
    }
    return FALSE;
};

// createClass

//去掉componentWillMount，componentWillReceiveProps，componentWillUpdate
let LifeCycles = ['componentDidMount', 'componentWillUnmount', 'shouldComponentUpdate', 'componentDidUpdate'],
    EventsFns = ['on', 'off', 'once', 'trigger'];

let fixedKey = (prefix, key) => prefix + key.replace(/\w/, (a) => a.toUpperCase());

let mergeEventFn = (em, obj) => {
    EventsFns.forEach((key) => {
        obj[key] = em[key].bind(em);
    });
    return obj;
};

const WEBX = 'webx';

let fixedLogic = (context, em, React, isView) => {
    defaults.globalPlugins
        .concat(context.plugins || [])
        .concat(context.constructor.plugins)
        .filter(
            (key, index, source) =>
                key && key[0] != '-' && source.indexOf(`-${key}`) == -1 && source.indexOf(key) == index
        )
        .forEach((name) => {
            if (!_isFunction(plugins[name]) || name === WEBX) return;
            plugins[name].call(em, context, context.constructor[`${name}Plugin`] || {}, React, isView);
        });

    LifeCycles.forEach((key) => {
        var originFn = context[key];
        context[key] = function () {
            let rs = key.indexOf('should') == 0 ? TRUE : NULL;
            em.trigger.apply(em, [fixedKey('before', key), this].concat(_makeArray(arguments)));
            if (_isFunction(originFn)) {
                try {
                    rs = originFn.apply(this, arguments);
                } catch (e) {
                    console.log('[QRN-Ext Error]', e);
                    throw e;
                }
            }
            em.trigger.apply(em, [fixedKey('after', key), this, rs].concat(_makeArray(arguments)));
            return rs;
        };
    });

    let bindEvents = context.bindEvents || {};

    for (let key in bindEvents) {
        if (_isFunction(bindEvents[key])) {
            em.on(key, bindEvents[key].bind(context));
        }
    }
};

let hookRender = function (context, em, React, isView) {
    let renderFn = context.render;
    let renderSkeleton = context.renderSkeleton;
    let disableRenderSkeleton = context.disableRenderSkeleton;

    let needInitWebXPlugin = true;
    let hasWebXPlugin =
        defaults.globalPlugins
            .concat(context.plugins || [])
            .concat(context.constructor.plugins)
            .filter(
                (key, index, source) =>
                    key && key[0] != '-' && source.indexOf(`-${key}`) == -1 && source.indexOf(key) == index
            )
            .indexOf(WEBX) > -1;

    context.render = function () {
        let rs = NULL;

        //render前加载webx plugin
        if (needInitWebXPlugin && hasWebXPlugin) {
            plugins[WEBX].call(em, context, context.constructor[`${WEBX}Plugin`] || {}, React, isView);
            needInitWebXPlugin = false;
        }

        em.trigger.apply(em, ['beforeRender', this].concat(_makeArray(arguments)));
        if (_isFunction(renderFn)) {
            try {
                rs = (() => {
                    // 降级处理
                    if (global.QrnConfig && global.QrnConfig['skeletonEnable'] === false) {
                        return renderFn.apply(this, arguments);
                    }

                    // 实现 disableRenderSkeleton 且返回 true 时 不渲染骨架屏
                    const disableRenderSkeletonRes =
                        disableRenderSkeleton && disableRenderSkeleton.apply(this, arguments);

                    if (!disableRenderSkeletonRes && renderSkeleton) {
                        // 骨架屏提升页面反应速度方案
                        if (!this.alreadySetTimeout) {
                            setTimeout(() => {
                                this.setState({
                                    _qrn_start_real_render: true
                                });
                            }, 1);

                            this.alreadySetTimeout = true;
                        }

                        this._qrnCloseSkeleton = () => {
                            this.setState({
                                _qrn_render_skeleton: true
                            });
                        };

                        return React.createElement(
                            React.Fragment,
                            {},
                            !this.state._qrn_render_skeleton ? renderSkeleton.apply(this, arguments) : null,
                            this.state._qrn_start_real_render ? renderFn.apply(this, arguments) : null
                        );
                    } else {
                        return renderFn.apply(this, arguments);
                    }
                }).apply(this, arguments);
            } catch (e) {
                console.log('[QRN-Ext Error]', e);
                throw e;
            }
        }
        em.trigger.apply(em, ['afterRender', this, rs].concat(_makeArray(arguments)));
        return rs;
    };
};

const addNativeEventListener = function (context) {
    const rootTag = context.props.rootTag || context.props.__rootTag_do_not_change_or_you_will_be_fired;

    if (!rootTag) {
        return;
    }
    const theFiber = context._reactInternals;
    // QRN ADD 挂载事件监听
    global.__QRNNativeListenerKeys &&
        global.__QRNNativeListenerKeys.forEach((item) => {
            if (theFiber.type.prototype[item]) {
                const eventName = `${rootTag}-${item}`;

                const theSub = DeviceEventEmitter.addListener(eventName, (data) => {
                    const result = theFiber.type.prototype[item].bind(context)();

                    if (result) {
                        let hybridid = '',
                            version = '';

                        if (global.__QP_INFO) {
                            hybridid = __QP_INFO['hybridid'];
                            version = __QP_INFO['version'];
                        }

                        const newResult = Object.assign(result, {
                            nativePageId: data.nativePageId,
                            name: data.name,
                            ext: {
                                hybridid,
                                version
                            }
                        });

                        // 调用 Native
                        NativeModules.QRCTRecoveryManager &&
                            NativeModules.QRCTRecoveryManager.saveRecoveryData(newResult, (cbData) => {
                                result.callback && result.callback(cbData);
                            });
                    }
                });

                if (nativeListenerMap[rootTag]) {
                    nativeListenerMap[rootTag].push(theSub);
                } else {
                    nativeListenerMap[rootTag] = [theSub];
                }
            }
        });

    // QRN END
};

let createComponent = (React, isView) => {
    class ExtComp extends React.Component {
        constructor(props, context) {
            let em = _createEventManager();
            super(props, context);

            hookRender(this, em, React, isView);

            // hook componentWillUnmount
            let originComponentWillUnmountFn = this.componentWillUnmount;
            this.componentWillUnmount = function () {
                if (props && props.rootTag) {
                    if (nativeListenerMap[props.rootTag]) {
                        nativeListenerMap[props.rootTag].forEach((sub) => {
                            sub && sub.remove();
                        });
                    }

                    delete nativeListenerMap[props.rootTag];
                }

                if (_isFunction(originComponentWillUnmountFn)) {
                    try {
                        originComponentWillUnmountFn.apply(this, arguments);
                    } catch (e) {
                        console.log('[QRN-Ext Error]', e);
                        throw e;
                    }
                }
            };

            // hook componentDidMount
            let originFn = this.componentDidMount;
            this.componentDidMount = function () {
                // this.name = isView ? this.constructor.name : NULL;
                fixedLogic(this, em, React, isView);
                let rs = null;
                em.trigger.apply(em, ['beforeComponentDidMount', this].concat(_makeArray(arguments)));
                if (_isFunction(originFn)) {
                    try {
                        rs = originFn.apply(this, arguments);
                    } catch (e) {
                        console.log('[QRN-Ext Error]', e);
                        throw e;
                    }
                }
                em.trigger.apply(em, ['afterComponentDidMount', this, rs].concat(_makeArray(arguments)));

                // 添加 native 事件监听逻辑
                addNativeEventListener(this);
            };
        }
        /**
         * 给当前页面设置别名
         * @param {string} name 别名
         * @return {boolean} 是否设置成功
         */
        setView(name) {
            let {
                Router: {
                    _hasAllocatedRootTagMap: hasAllocatedRootTagMap,
                    _lifecycleListeners: lifecycleListeners,
                    _hasInvokedSetViewNameKey: hasInvokedSetViewNameKey
                }
            } = Ext;
            // 当前页面分配的 rootTag
            const { [rootTagKey]: rootTag } = this.props;
            if (!hasAllocatedRootTagMap[rootTag]) {
                // 只能给已经打开过的页面设置，即能到当前页面的 rootTag
                return false;
            }
            // 当前页面名字
            const prevName = hasAllocatedRootTagMap[rootTag];
            NativeRouter.setViewName(rootTag, name);
            hasAllocatedRootTagMap[rootTag] = name;
            // 保留改后的 viewKey -> prevKey 的引用
            const viewKey = `${name}_${rootTag}`;
            const prevKey = `${prevName}_${rootTag}`;
            hasInvokedSetViewNameKey[viewKey] = prevKey;
            // 保证改后别名的页面的生命周期正常执行
            lifecycleListeners = {
                ...lifecycleListeners,
                [viewKey]: lifecycleListeners[prevKey]
            };
            delete lifecycleListeners[prevKey];
            Ext.Router._lifecycleListeners = lifecycleListeners;
            return true;
        }
    }
    ExtComp.type = isView ? 'View' : 'Component';
    return ExtComp;
};

let register = (ExtClass, ExtClassName) => {
    let finalClass = registerCallbacks.reduce((Class, fn) => {
        const type = (Class.WrappedComponent && Class.WrappedComponent.type) || Class.type;
        return (
            fn(
                Class,
                type == 'View',
                defaults.globalPlugins
                    .concat(Class.plugins || [])
                    .filter(
                        (key, index, source) =>
                            key && key[0] != '-' && source.indexOf(`-${key}`) == -1 && source.indexOf(key) == index
                    ),
                ExtClassName
            ) || Class
        );
    }, ExtClass);
    viewMap[ExtClassName] = finalClass;
    return finalClass;
};

let init = (rn) => {
    let react = rn || React;
    if (react) {
        cache.View = createComponent(react, TRUE);
        cache.Component = createComponent(react, FALSE);
        initFns.forEach((fn) => fn(react));
    } else {
        console.error('[QRN-Ext Error]', 'Not Found React');
    }
};

let getCache = (name) => {
    if (!cache[name]) {
        init();
    }
    return cache[name];
};

let defineStateProperty = (obj, attr, fn) => {
    Object.defineProperty(obj, attr, {
        get: fn,
        set: _noop
    });
};

defineStateProperty(Ext, 'View', () => getCache('View'));
defineStateProperty(Ext, 'Component', () => getCache('Component'));
defineStateProperty(React, 'QView', () => getCache('View'));
defineStateProperty(React, 'QComponent', () => getCache('Component'));
defineStateProperty(global, 'QView', () => getCache('View'));
defineStateProperty(global, 'QComponent', () => getCache('Component'));

Ext.init = init;

/**
 * 添加插件函数
 * @param  {String}     name             插件名
 * @param  {Function}   adapter          适配函数
 * @param  {Function}   initFn           Ext 初始化回调函数
 * @param  {Function}   registerFn       View 注册回调函数
 */
Ext.addPlugin = (name, adapter, initFn, registerFn) => {
    if (_isString(name)) {
        plugins[name] = adapter;
        if (_isFunction(initFn)) {
            initFns.push(initFn);
        }
        if (_isFunction(registerFn)) {
            registerCallbacks.push(registerFn);
        }
    }
};

Ext.register = register;

Ext._registerCallbacks = registerCallbacks;

Ext.viewMap = viewMap;

Ext._cache = cache;

Ext.utils = {
    getType: getType,
    isObject: _isObject,
    isArray: _isArray,
    isString: _isString,
    isFunction: _isFunction,
    isNumber: _isNumber,
    isPlainObject: _isPlainObject,
    isEmptyObject: _isEmptyObject,
    noop: _noop,
    extend: _extend,
    createEventManager: _createEventManager,
    CustEvent: CustEvent,
    startWith: _startWith,
    endWith: _endWith,
    sliceFn: _sliceFn
};

global.Ext = Ext;

export default Ext;
